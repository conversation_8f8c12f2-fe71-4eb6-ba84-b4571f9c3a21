-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "kingdom_sis";

-- CreateEnum
CREATE TYPE "kingdom_sis"."StudentStatus" AS ENUM ('active', 'inactive', 'suspended', 'graduated');

-- CreateEnum
CREATE TYPE "kingdom_sis"."EnrollmentType" AS ENUM ('fulltime', 'parttime', 'online', 'hybrid');

-- CreateTable
CREATE TABLE "kingdom_sis"."users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password_hash" TEXT,
    "name" TEXT NOT NULL,
    "avatar" TEXT,
    "school" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."schools" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "address" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "website" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "schools_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."departments" (
    "id" TEXT NOT NULL,
    "school_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "departments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."students" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "schoolId" TEXT NOT NULL,
    "studentId" TEXT NOT NULL,
    "status" "kingdom_sis"."StudentStatus" NOT NULL DEFAULT 'active',
    "enrollmentType" "kingdom_sis"."EnrollmentType" NOT NULL DEFAULT 'fulltime',
    "grade" TEXT,
    "year" INTEGER,
    "departmentId" TEXT,
    "enrollmentDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "graduationDate" TIMESTAMP(3),
    "gpa" DECIMAL(65,30),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "students_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."roles" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "isSystem" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."permissions" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "resource" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."role_permissions_assignment" (
    "id" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "permissionId" TEXT NOT NULL,

    CONSTRAINT "role_permissions_assignment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kingdom_sis"."user_roles_assignment" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,

    CONSTRAINT "user_roles_assignment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "kingdom_sis"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "schools_code_key" ON "kingdom_sis"."schools"("code");

-- CreateIndex
CREATE UNIQUE INDEX "departments_school_id_code_key" ON "kingdom_sis"."departments"("school_id", "code");

-- CreateIndex
CREATE UNIQUE INDEX "students_userId_key" ON "kingdom_sis"."students"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "students_studentId_key" ON "kingdom_sis"."students"("studentId");

-- CreateIndex
CREATE UNIQUE INDEX "roles_name_key" ON "kingdom_sis"."roles"("name");

-- CreateIndex
CREATE UNIQUE INDEX "permissions_name_key" ON "kingdom_sis"."permissions"("name");

-- CreateIndex
CREATE INDEX "role_permissions_assignment_roleId_permissionId_idx" ON "kingdom_sis"."role_permissions_assignment"("roleId", "permissionId");

-- CreateIndex
CREATE INDEX "user_roles_assignment_userId_roleId_idx" ON "kingdom_sis"."user_roles_assignment"("userId", "roleId");

-- AddForeignKey
ALTER TABLE "kingdom_sis"."departments" ADD CONSTRAINT "departments_school_id_fkey" FOREIGN KEY ("school_id") REFERENCES "kingdom_sis"."schools"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."students" ADD CONSTRAINT "students_userId_fkey" FOREIGN KEY ("userId") REFERENCES "kingdom_sis"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."students" ADD CONSTRAINT "students_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "kingdom_sis"."schools"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."students" ADD CONSTRAINT "students_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "kingdom_sis"."departments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."role_permissions_assignment" ADD CONSTRAINT "role_permissions_assignment_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "kingdom_sis"."roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."role_permissions_assignment" ADD CONSTRAINT "role_permissions_assignment_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "kingdom_sis"."permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."user_roles_assignment" ADD CONSTRAINT "user_roles_assignment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "kingdom_sis"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kingdom_sis"."user_roles_assignment" ADD CONSTRAINT "user_roles_assignment_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "kingdom_sis"."roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

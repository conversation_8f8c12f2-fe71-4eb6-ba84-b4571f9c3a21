import type React from "react";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import AppSidebar from "./_components/navigation/app-sidebar";
import { AppHeader } from "./_components/navigation/app-header";
import { notFound, redirect } from "next/navigation";
import { auth } from "@/lib/auth";
import { Resources } from "@/lib/permissions";
import { SessionProvider } from "next-auth/react";
import { User } from "@/lib/types";
import { getUser } from "@/lib/server/users/users.api";

export default async function AppLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ school: string }>;
}) {
  const { school: schoolSlug } = await params;
  const session = await auth();

  if (!session || !session.user) {
    redirect("/login");
  }

  const user = (await getUser(session.user.id)) as User;

  if (session.user.school !== schoolSlug || !user) {
    notFound();
  }

  if (
    !user.permissions.some(
      (p) =>
        (p.resource === Resources.DASHBOARD || p.resource === "*") &&
        (p.action === "read" || p.action === "manage")
    )
  ) {
    redirect("/unauthorized?reason=dashboard_access");
  }

  return (
    <SessionProvider>
      <SidebarProvider>
        <AppSidebar schoolSlug={schoolSlug} user={user} />

        <SidebarInset>
          <AppHeader user={user} />
          <div className="flex-1 overflow-y-auto p-6">{children}</div>
        </SidebarInset>
      </SidebarProvider>
    </SessionProvider>
  );
}

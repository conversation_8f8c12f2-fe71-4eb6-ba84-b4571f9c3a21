import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as { roleId: string; permissionIds: string[] };

    await prisma.$transaction(async (tx) => {
      await tx.rolePermissionAssignment.deleteMany({
        where: { roleId: body.roleId },
      });
      await tx.rolePermissionAssignment.createMany({
        data: body.permissionIds.map((permissionId: string) => ({
          roleId: body.roleId,
          permissionId,
        })),
      });
    });
    
    return NextResponse.json({ success: true, message: "Permissions assigned" });
  } catch (error) {
    console.error("Error assigning permission:", error);
    return NextResponse.json(
      { error: "Failed to assign permission" },
      { status: 500 },
    );
  }
}
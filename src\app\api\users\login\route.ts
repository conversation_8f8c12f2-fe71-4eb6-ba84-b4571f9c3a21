import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  const body = await request.json() as { email: string };

  if (!body.email) {
    return NextResponse.json(
      { error: "Email and password are required" },
      { status: 400 },
    );
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: body.email },
      include: {
        userRoles: {
          select: {
            id: true,
            role: {
              select: {
                id: true,
              },
            }
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 },
      );
    }

    const transformedUsers = {
      id: user.id,
      email: user.email,
      school: user.school,
      roles: user.userRoles.map((role) => ({
        id: role.role.id,
      })),
    };

    return NextResponse.json(transformedUsers);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}
import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";

export async function GET(request: NextRequest) {
  const body = await request.json() as { email: string };

  if (!body.email) {
    return NextResponse.json(
      { error: "Email and password are required" },
      { status: 400 },
    );
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: body.email },
      include: {
        userRoles: {
          select: {
            id: true,
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
                rolePermissions: {
                  select: {
                    id: true,
                    permission: {
                      select: {
                        id: true,
                        name: true,
                        description: true,
                        resource: true,
                        action: true,
                      },
                    },
                  },
                },
              },
            }
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 },
      );
    }

    const transformedUsers = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      school: user.school,
      roles: user.userRoles.map((role) => ({
        id: role.role.id,
        name: role.role.name,
        displayName: role.role.displayName,
      })),
      permissions: user.userRoles
        .map((role) => role.role.rolePermissions)
        .flat()
        .map((permission) => permission.permission.name),
    };

    return NextResponse.json(transformedUsers);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}
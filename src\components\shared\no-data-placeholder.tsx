"use client";

import { Button } from "@/components/ui/button";

export default function NoDataPlaceholder({
  title = "No Data Found",
  description = "There is no data to display.",
  children,
}: {
  title?: string;
  description?: string;
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col items-center justify-center py-8 text-center border shadow-sm rounded-lg">
      {/* Icon */}
      <div className="mb-4">
        <div className="w-16 h-16 flex flex-col items-center justify-center space-y-1">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-accent rounded-full"></div>
            <div className="w-10 h-1 bg-accent rounded"></div>
          </div>
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-accent rounded-full"></div>
            <div className="w-8 h-1 bg-accent rounded"></div>
          </div>
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-accent rounded-full"></div>
            <div className="w-6 h-1 bg-accent rounded"></div>
          </div>
        </div>
      </div>

      {/* Text Content */}
      <h2 className="text-xl font-semibold">{title}</h2>
      <p className="text-sm text-muted-foreground mb-8 max-w-md">
        {description}
      </p>

      {children}
    </div>
  );
}

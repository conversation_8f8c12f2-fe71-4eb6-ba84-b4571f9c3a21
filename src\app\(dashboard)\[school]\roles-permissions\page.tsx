import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Shield, Users, Settings, History } from "lucide-react";
import { RoleManagement } from "./_components/role.managemant";
import { PermissionManagement } from "./_components/permission-management";
import { UserRoleAssignment } from "./_components/user-role-asignment";
import { AuditTrail } from "./_components/audit-trail";
import StatsCard from "./_components/stats-card";
import PageWrapper from "../_components/layouts/PageWrapper";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Resources } from "@/lib/permissions";
import { auth } from "@/lib/auth";
import { getUser, getUsers } from "@/lib/server/users/users.api";
import {
  getPermissions,
  getRoles,
  getRoleOptions,
} from "@/lib/server/roles/roles.api";

const breadcrumbItems = [
  { label: "Home", href: "/" },
  { label: "Role & Permission Management" },
];

async function RolePermissionDashboard() {
  const session = await auth();

  const [userData, roles, permissions, roleOptions] = await Promise.all([
    getUsers(),
    getRoles(),
    getPermissions(),
    getRoleOptions(),
  ]);

  return (
    <PageWrapper
      pgTitle="Role & Permission Management"
      pgDescription="Manage user roles and permissions for your school management system"
      breadcrumbItems={breadcrumbItems}
      school="kingdom"
    >
      {/* Stats Cards */}
      <StatsCard />

      {/* Main Content Tabs */}
      <Tabs defaultValue="roles" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Permissions
          </TabsTrigger>
          <TabsTrigger value="assignments" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            User Assignment
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Audit Trail
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles">
          <RoleManagement roles={roles} />
        </TabsContent>

        <TabsContent value="permissions">
          <PermissionManagement
            permissions={permissions}
            roleOptions={roleOptions}
          />
        </TabsContent>

        <TabsContent value="assignments">
          <UserRoleAssignment initialUsers={userData} roles={roles} />
        </TabsContent>

        <TabsContent value="audit">
          <AuditTrail />
        </TabsContent>
      </Tabs>
    </PageWrapper>
  );
}

export default withResourceAccess(RolePermissionDashboard, {
  resource: Resources.ROLES,
});

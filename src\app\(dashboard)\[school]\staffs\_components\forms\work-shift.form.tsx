"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { toast } from "sonner";
import { FormInput, FormSelect, FormDatePicker } from "@/components/forms";
import z from "zod";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { staffDepartmentOptions } from "@/lib/mock-data";

interface WorkShiftInfoFormProps {
  initialData?: Partial<WorkShiftInfoFormData>;
}

const workShiftInfoSchema = z.object({
  id: z.string().optional(),
  workShift: z.string().min(2, "Select a workShift"),
  startDate: z.date({
    error: "Start date is required",
  }),
  endDate: z.date().optional(),
  remarks: z.string().optional(),
});

type WorkShiftInfoFormData = z.infer<typeof workShiftInfoSchema>;

export function WorkShiftInfoForm({ initialData }: WorkShiftInfoFormProps) {
  const form = useForm<WorkShiftInfoFormData>({
    resolver: zodResolver(workShiftInfoSchema),
    defaultValues: {
      workShift: undefined,
      startDate: new Date(),
      endDate: new Date(),
      remarks: "",
      ...initialData,
    },
  });

  const handleSubmit = async (data: WorkShiftInfoFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* WorkShift Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormSelect
                  form={form}
                  name="workShift"
                  label="Work Shift"
                  placeholder="Select work shift"
                  options={staffDepartmentOptions}
                />
                <FormDatePicker
                  form={form}
                  name="startDate"
                  label="Start Date"
                  placeholder="Select start date"
                />
                <FormDatePicker
                  form={form}
                  name="endDate"
                  label="End Date"
                  placeholder="Select end date"
                />
              </div>
              <FormInput
                form={form}
                name="remarks"
                label="Remarks"
                placeholder="Enter remarks"
              />
            </div>

            {/* Form Actions */}
            <div className="flex pt-4">
              <Button
                type="submit"
                className="flex-1 md:flex-none"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>Submit</>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

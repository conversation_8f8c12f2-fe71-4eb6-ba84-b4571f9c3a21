"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { Actions, Resources } from "@/lib/permissions";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { getLocalDateString } from "@/lib/date-converter";
import CustomDialog from "@/components/shared/CustomDialog";
import { EmploymentInfoForm } from "../../_components/forms/employment.form";
import type { WorkShift } from "../../_types/type";
import { WorkShiftInfoForm } from "../../_components/forms/work-shift.form";

export const columns: ColumnDef<WorkShift>[] = [
  {
    accessorKey: "workShift",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Work Shift" />
    ),
  },
  {
    accessorKey: "startDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Period" />
    ),
    cell: ({ row }) => {
      const startDate = row.original.startDate as Date;
      const endDate = row.original.endDate as Date | null;
      return (
        <div className="flex flex-wrap gap-2">
          <p>{getLocalDateString(startDate)}</p>
          <p className="text-sm text-muted-foreground">-</p>
          <p>{endDate ? getLocalDateString(endDate) : "Present"}</p>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    id: "actions",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => {
      const workShift = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="data-[state=open]:bg-muted size-8"
            >
              <MoreHorizontal />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            <DropdownMenuItem>View</DropdownMenuItem>
            <ClientRoleGuard
              resource={Resources.STAFFS}
              action={Actions.MANAGE}
            >
              <DropdownMenuItem asChild>
                <CustomDialog
                  title="Edit Work Shift"
                  description="Edit the work shift"
                  asChild={false}
                  trigger={<span>Edit</span>}
                >
                  <WorkShiftInfoForm
                    initialData={{
                      id: workShift.id,
                      startDate: workShift.startDate,
                      endDate: workShift.endDate || undefined,
                      workShift: workShift.workShift,
                    }}
                  />
                </CustomDialog>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <CustomAlertDialog
                  title="Delete WorkShift"
                  description="Are you sure you want to delete this workShift? This action cannot be undone."
                  onConfirm={() => console.log("Delete clicked")}
                  className="w-full"
                  asChild={false}
                  trigger={<span>Delete</span>}
                />
                {/* <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut> */}
              </DropdownMenuItem>
            </ClientRoleGuard>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

import { PrismaClient } from "@prisma/client";
import { Resources, Actions, SYSTEM_ROLES, ALL_PERMISSIONS } from "../src/lib/permissions";

const prisma = new PrismaClient();

async function seedRolesAndPermissions() {
  console.log("🌱 Starting roles and permissions seeding...");

  // Clean existing role and permission data
  console.log("🧹 Cleaning existing roles and permissions data...");
  await prisma.userRoleAssignment.deleteMany();
  await prisma.rolePermissionAssignment.deleteMany();
  await prisma.role.deleteMany();
  await prisma.permission.deleteMany();

  // Create permissions
  console.log("🔐 Creating permissions...");
  const createdPermissions = [];
  for (const permission of ALL_PERMISSIONS) {
    const createdPermission = await prisma.permission.create({
      data: {
        name: permission.name,
        description: permission.description,
        resource: permission.resource,
        action: permission.action,
      },
    });
    createdPermissions.push(createdPermission);
  }

  console.log(`   ✅ Created ${createdPermissions.length} permissions`);

  // Create roles
  console.log("👥 Creating roles...");
  const createdRoles = [];
  for (const [, roleData] of Object.entries(SYSTEM_ROLES)) {
    const role = await prisma.role.create({
      data: {
        name: roleData.name,
        displayName: roleData.displayName,
        description: roleData.description,
        isSystem: roleData.isSystem,
        isActive: roleData.isActive,
      },
    });
    createdRoles.push({ ...role, permissions: roleData.permissions });
  }

  console.log(`   ✅ Created ${createdRoles.length} roles`);

  // Create role-permission assignments
  console.log("🔗 Creating role-permission assignments...");
  let totalAssignments = 0;
  
  for (const role of createdRoles) {
    for (const rolePermission of role.permissions) {
      // Handle wildcard permissions (super admin)
      if (rolePermission.resource === "*" && rolePermission.action === "manage") {
        // Assign all permissions to super admin
        for (const permission of createdPermissions) {
          await prisma.rolePermissionAssignment.create({
            data: {
              roleId: role.id,
              permissionId: permission.id,
            },
          });
          totalAssignments++;
        }
      } else {
        // Find matching permission
        const matchingPermission = createdPermissions.find(
          p => p.resource === rolePermission.resource && p.action === rolePermission.action
        );
        
        if (matchingPermission) {
          await prisma.rolePermissionAssignment.create({
            data: {
              roleId: role.id,
              permissionId: matchingPermission.id,
            },
          });
          totalAssignments++;
        } else {
          console.warn(`   ⚠️  Permission not found: ${rolePermission.resource}:${rolePermission.action}`);
        }
      }
    }
  }

  console.log(`   ✅ Created ${totalAssignments} role-permission assignments`);

  // Get final counts
  const roleCount = await prisma.role.count();
  const permissionCount = await prisma.permission.count();
  const rolePermissionCount = await prisma.rolePermissionAssignment.count();

  console.log("✅ Roles and permissions seeding completed successfully!");
  console.log(`📊 Created:`);
  console.log(`   👥 Roles: ${roleCount}`);
  console.log(`   🔐 Permissions: ${permissionCount}`);
  console.log(`   🔗 Role-Permission Assignments: ${rolePermissionCount}`);

  // Show sample data
  console.log("\n📋 Sample roles and their permission counts:");
  const rolesWithPermissions = await prisma.role.findMany({
    include: {
      _count: {
        select: { rolePermissions: true },
      },
    },
  });

  rolesWithPermissions.forEach((role, index) => {
    console.log(
      `   ${index + 1}. ${role.displayName} (${role.name}) - ${role._count.rolePermissions} permissions`,
    );
  });

  return {
    roles: createdRoles,
    permissions: createdPermissions,
    assignments: totalAssignments,
  };
}

// Run the seeding function
seedRolesAndPermissions()
  .catch((e) => {
    console.error("❌ Error during roles and permissions seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

export { seedRolesAndPermissions };

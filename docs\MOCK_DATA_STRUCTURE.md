# Mock Data Structure Documentation

## Overview

The mock data system provides comprehensive seed data for testing and development of the Kingdom SIS Web application. It includes users with proper role assignments, permissions, and relationships between different entities.

## Mock Users Structure

### Core Mock Users (20 users)

#### Super Admin (1 user)
- **<PERSON>** (`<EMAIL>`)
  - Role: Super Admin
  - Permissions: Full access to all resources

#### School Admins (3 users)
- **<PERSON>** (`<EMAIL>`)
- **<PERSON>** (`<EMAIL>`)
- **<PERSON>** (`<EMAIL>`)
  - Role: School Admin
  - Permissions: Manage students, teachers, classes, settings, users, roles

#### Principal (1 user)
- **Dr. <PERSON>** (`<EMAIL>`)
  - Role: Principal
  - Permissions: Similar to admin with academic oversight focus

#### Teachers (5 users)
- **Dr. <PERSON>** (`<EMAIL>`) - Mathematics & Physics
- **Prof. <PERSON>** (`<EMAIL>`) - English & Literature
- **Dr. <PERSON>** (`<EMAIL>`) - Chemistry & Biology
- **<PERSON>. <PERSON>** (`<EMAIL>`) - History & Social Studies
- **Mr. <PERSON>** (`<EMAIL>`) - Mathematics & Computer Science
  - Role: Teacher
  - Permissions: Manage grades and attendance for own classes

#### Students (4 users)
- **Emily Davis** (`<EMAIL>`) - Grade 10
- **Alex Johnson** (`<EMAIL>`) - Grade 11
- **Sophia Martinez** (`<EMAIL>`) - Grade 9
- **Lucas Brown** (`<EMAIL>`) - Grade 12
  - Role: Student
  - Permissions: Read own grades, attendance, and enrolled classes

#### Parents (3 users)
- **Robert Davis** (`<EMAIL>`) - Parent of Emily & Lucas
- **Maria Martinez** (`<EMAIL>`) - Parent of Sophia
- **James Johnson** (`<EMAIL>`) - Parent of Alex
  - Role: Parent
  - Permissions: Read children's information, grades, and attendance

#### Applicants (3 users)
- **Alice Doe** (`<EMAIL>`)
- **Tom Wilson** (`<EMAIL>`)
- **Emma Clark** (`<EMAIL>`)
  - Role: Applicant
  - Permissions: Manage enrollment forms, read payment info, access help

### Additional Generated Users (100 users)

The system also generates 100 additional users using Faker.js with the following distribution:
- **60% Students** - Various grades and classes
- **20% Teachers** - Different subjects and departments
- **15% Parents** - Linked to student children
- **4% Applicants** - Prospective students
- **1% Admins** - Additional administrative staff

## Role-Permission Mapping

### System Roles with Proper Permissions

Each user has:
- **roles**: Array of role objects with `id`, `name`, and `displayName`
- **permissions**: Array of permission objects from `SYSTEM_ROLES`

Example user structure:
```typescript
{
  id: "1",
  email: "<EMAIL>",
  name: "User Name",
  roles: [
    {
      id: "4",
      name: "teacher",
      displayName: "Teacher"
    }
  ],
  permissions: SYSTEM_ROLES.TEACHER.permissions,
  avatar: "/avatar.png",
  school: "kingdom",
  createdAt: Date,
  updatedAt: Date
}
```

## Related Mock Data

### Classes (8 classes)
- Grades 9-12 with sections A and B
- Assigned to appropriate teachers
- Linked to enrolled students

### Subjects (9 subjects)
- Mathematics, Physics, English Literature, Chemistry, Biology
- History, Social Studies, Computer Science, Advanced Mathematics
- Assigned to qualified teachers

### Assignments (4 assignments)
- Algebra Quiz, Physics Lab Report, English Essay, Chemistry Test
- Linked to appropriate subjects, classes, and teachers

### Grades (4 grade records)
- Student performance on assignments
- Includes scores, feedback, and grading dates

### Attendance (6 attendance records)
- Daily attendance tracking for students
- Includes present, late, and absent statuses

## Usage Examples

### Getting Users by Role
```typescript
import { allMockUsers } from '@/lib/mock-data';

// Get all teachers
const teachers = allMockUsers.filter(user => 
  user.roles.some(role => role.name === 'teacher')
);

// Get all students
const students = allMockUsers.filter(user => 
  user.roles.some(role => role.name === 'student')
);
```

### Permission Checking
```typescript
import { checkPermission } from '@/lib/permissions';
import { Resources, Actions } from '@/lib/permissions';

const user = allMockUsers[0]; // Super Admin
const canManageStudents = checkPermission(
  user, 
  Resources.STUDENTS, 
  Actions.MANAGE
); // true
```

### Finding Related Data
```typescript
import { getUserById, getClassesByTeacher } from '@/lib/mock-data';

const teacher = getUserById("4"); // Dr. John Smith
const teacherClasses = getClassesByTeacher("4"); // Classes taught by John Smith
```

## Data Relationships

### Parent-Child Relationships
- Robert Davis (ID: 12) → Emily Davis (ID: 9), Lucas Brown (ID: 20)
- Maria Martinez (ID: 13) → Sophia Martinez (ID: 11)
- James Johnson (ID: 14) → Alex Johnson (ID: 10)

### Teacher-Class Assignments
- Dr. John Smith → 10A (Mathematics, Physics), 11B (Mathematics)
- Prof. Jane Doe → 9A (English Literature), 10A (English)
- Dr. Mike Wilson → 11A, 12A (Chemistry, Biology)
- Ms. Lisa Brown → 9B, 10B (History, Social Studies)
- Mr. David Garcia → 11B, 12B (Mathematics, Computer Science)

### Student-Class Enrollments
- Emily Davis → 10A
- Alex Johnson → 11A
- Sophia Martinez → 9A
- Lucas Brown → 12A

## Benefits

1. **Comprehensive Testing**: Covers all user roles and permission scenarios
2. **Realistic Data**: Uses Faker.js for realistic names, emails, and dates
3. **Proper Relationships**: Maintains referential integrity between entities
4. **Role-Based Access**: Each user has appropriate permissions for their role
5. **Scalable**: Easy to generate additional users as needed
6. **Development Ready**: Immediately usable for UI development and testing

## Extending Mock Data

To add more users or modify existing data:

1. **Add Static Users**: Extend the `mockUsers` array
2. **Generate Dynamic Users**: Use `generateMockUsers()` function
3. **Update Relationships**: Modify classes, subjects, and assignments accordingly
4. **Maintain Consistency**: Ensure role-permission mappings are correct

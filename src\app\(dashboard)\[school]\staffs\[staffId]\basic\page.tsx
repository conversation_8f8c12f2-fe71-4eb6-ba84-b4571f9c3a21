import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Actions, Resources } from "@/lib/permissions";
import { BasicInfoForm } from "../../_components/forms/basic.form";
import type { ContactInfo } from "../../_types/type";

const BasicInfo = {
  firstName: "Bagwati",
  middleName: "<PERSON><PERSON>",
  lastName: "Rege",
  employeeCode: "ESM001",
  dateOfJoining: new Date("2022-01-01"),
  department: "Administration Department",
  designation: "Administrator",
  dateOfBirth: new Date("1990-10-05"),
  gender: "female",
  nationality: "PH",
  stateOfOrigin: "Abra",
  stateOfResidence: "Abra",
  religiousAffiliation: "christianity",
  bloodGroup: "O+",
  maritalStatus: "single",
  staffType: "teaching",
} as ContactInfo;

const Tags = [
  { label: "Tag 1", color: "blue" },
  { label: "Tag 2", color: "green" },
  { label: "Tag 3", color: "red" },
];

const EmployeeGroup = [
  { label: "Group 1", color: "blue" },
  { label: "Group 2", color: "green" },
  { label: "Group 3", color: "red" },
];

function BasicPage() {
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-xl font-semibold">Basic Information</h1>
        <ClientRoleGuard
          resource={Resources.STAFFS}
          action={Actions.UPDATE}
          className="flex gap-2"
        >
          <CustomDialog
            title="Basic Information"
            description="Edit basic information of the staff"
            trigger={<Button variant="outline">Edit</Button>}
          >
            <BasicInfoForm initialData={BasicInfo} />
          </CustomDialog>
          <CustomDialog
            title="Edit Photo"
            description="Upload a new photo for the staff"
            trigger={<Button variant="outline">Edit Photo</Button>}
          >
            <div>Edit Photo Form</div>
          </CustomDialog>
        </ClientRoleGuard>
      </div>

      <Card>
        <CardContent className="px-6">
          <div className="grid grid-cols-4 gap-4">
            {Object.entries(BasicInfo).map(([key, value]) => (
              <div key={key}>
                <label className="text-sm font-medium text-muted-foreground">
                  {key}
                </label>
                <p className="mt-1 text-sm">
                  {value instanceof Date
                    ? value.toLocaleDateString()
                    : value || "-"}
                </p>
              </div>
            ))}

            {/* Row 5 */}
            <div className="col-span-4">
              <label className="text-sm font-medium text-muted-foreground">
                Tags 🔗
              </label>
              {Tags.length === 0 ? (
                <p className="mt-1">-</p>
              ) : (
                <div className="mt-1 flex flex-wrap gap-2">
                  {Tags.map((tag) => (
                    <span
                      key={tag.label}
                      className={`px-2 py-1 rounded-full text-xs bg-${tag.color}-100 text-${tag.color}-600`}
                    >
                      {tag.label}
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Row 6 */}
            <div className="col-span-4">
              <label className="text-sm font-medium text-muted-foreground">
                Employee Group
              </label>
              <p className="mt-1 text-sm">{EmployeeGroup[0].label}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}

export default withResourceAccess(BasicPage, {
  resource: Resources.STAFFS,
});

import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";

const DocumentInfo = [
  {
    degree: "B.Tech",
    specialization: "Computer Science",
    institution: "XYZ University",
    yearOfPassing: "2022",
    percentage: "85%",
  },
  {
    degree: "M.Tech",
    specialization: "Artificial Intelligence",
    institution: "ABC University",
    yearOfPassing: "2024",
    percentage: "90%",
  },
];

function DocumentPage() {
  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Document</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2 bg-transparent"
          >
            Add Document
          </Button>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {DocumentInfo.length !== 0 ? (
        <NoDataPlaceholder
          title="Manage all Employee Document Records"
          description="Keep the documents related to your staff's document."
        >
          <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
            <CustomDialog
              title="Add Document"
              description="Add a new document for the staff"
              trigger={<Button>Add Document</Button>}
            >
              <div>Add Document Form</div>
            </CustomDialog>
          </ClientRoleGuard>
        </NoDataPlaceholder>
      ) : (
        <div>
          {DocumentInfo.map((document) => (
            <div key={document.degree}>
              <h2 className="text-lg font-semibold">{document.degree}</h2>
              <p className="text-sm text-muted-foreground">
                {document.specialization}
              </p>
              <p className="text-sm text-muted-foreground">
                {document.institution}
              </p>
              <p className="text-sm text-muted-foreground">
                Year of Passing: {document.yearOfPassing}
              </p>
              <p className="text-sm text-muted-foreground">
                Percentage: {document.percentage}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default withResourceAccess(DocumentPage, {
  resource: Resources.STAFFS,
});

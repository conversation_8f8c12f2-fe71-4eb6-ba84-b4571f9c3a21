import { login } from "@/lib/server/users/users.api";
import { permissions, UserRole } from "@/lib/types";
import NextAuth, { User } from "next-auth";
import Credentials from "next-auth/providers/credentials";

export const { handlers, signIn, signOut, auth } = NextAuth({
  session: {
    strategy: "jwt",
  },
  providers: [
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials, req) => {
        if (!credentials.email || !credentials.password) return null;
        const { email, password } = credentials as {
          email: string;
          password: string;
        };

        const user = await login(email);
        if (user && password === "password123") {
          // Map your user object to the NextAuth User type
          return user
        }
        return null;
      },
    }),
  ],
  pages: {
    signIn: "/login",
  },
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.roles = user.roles;
        token.school = user.school;
      }
      return token;
    },
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.roles = token.roles as UserRole[];
        session.user.school = token.school as string;
      }
      return session;
    },
  },
});

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      roles: UserRole[];
      school: string;
    };
  }
  interface User {
    id: string;
    email: string;
    roles: UserRole[];
    school: string;
  }
  interface JWT {
    id: string;
    email: string;
    roles: UserRole[];
    school: string;
  }
}

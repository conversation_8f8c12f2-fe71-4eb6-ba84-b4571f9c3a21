import { login } from "@/lib/server/users/users.api";
import { permissions, UserRole } from "@/lib/types";
import NextAuth, { User } from "next-auth";
import Credentials from "next-auth/providers/credentials";

export const { handlers, signIn, signOut, auth } = NextAuth({
  session: {
    strategy: "jwt",
  },
  providers: [
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials, req) => {
        if (!credentials.email || !credentials.password) return null;
        const { email, password } = credentials as {
          email: string;
          password: string;
        };

        const user = await login(email);
        if (user && password === "password123") {
          // Map your user object to the NextAuth User type
          return user
        }
        return null;
      },
    }),
  ],
  pages: {
    signIn: "/login",
  },
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.email = user.email;
        token.roles = user.roles;
        token.permissions = user.permissions;
        token.school = user.school;
        token.avatar = user.avatar;
      }
      return token;
    },
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.roles = token.roles as UserRole[];
        session.user.permissions = token.permissions as permissions[];
        session.user.school = token.school as string;
        session.user.avatar = token.avatar as string | undefined;
      }
      return session;
    },
  },
});

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      roles: UserRole[];
      permissions: permissions[];
      school: string;
      avatar?: string;
    };
  }
  interface User {
    id: string;
    email: string;
    name: string;
    roles: UserRole[];
    permissions: permissions[];
    school: string;
    avatar?: string;
  }
  interface JWT {
    id: string;
    email: string;
    name: string;
    roles: UserRole[];
    permissions: permissions[];
    school: string;
    avatar?: string;
  }
}

import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Actions, Resources } from "@/lib/permissions";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { ContactInfoForm } from "../../_components/forms/contact.form";

const ContactInfo = {
  contactNumber: "09877322288",
  alternateContactNumber: "",
  email: "<EMAIL>",
  alternateEmail: "",
  emergencyContactName: "",
  emergencyContactNumber: "",
  emergencyContactRelation: "",
  presentAddress: {
    address1: "123 Main St",
    address2: "",
    barangay: "",
    city: "",
    province: "",
    country: "PH",
    postalCode: "",
  },
  permanentAddress: {
    address1: "123 Main St",
    address2: "",
    barangay: "",
    city: "",
    province: "",
    country: "PH",
    postalCode: "",
    sameAsPresentAddress: true,
  },
};

function ContactPage() {
  // convert presentAddress and permanentAddress to string
  const presentAddress = Object.values(ContactInfo.presentAddress)
    .filter(Boolean)
    .join(", ");
  const permanentAddress = Object.values(ContactInfo.permanentAddress)
    .filter(Boolean)
    .join(", ");

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-xl font-semibold">Contact Information</h1>
        <ClientRoleGuard resource={Resources.STAFFS} action={Actions.UPDATE}>
          <CustomDialog
            title="Contact Information"
            description="Edit contact information of the staff"
            trigger={<Button variant="outline">Edit</Button>}
          >
            <ContactInfoForm initialData={ContactInfo} />
          </CustomDialog>
        </ClientRoleGuard>
      </div>

      <Card>
        <CardContent className="px-6">
          <div className="grid grid-cols-3 gap-4">
            {Object.entries(ContactInfo)
              .slice(0, -2)
              .map(([key, value]) => (
                <div key={key}>
                  <label className="text-sm font-medium text-muted-foreground">
                    {key}
                  </label>
                  <p className="mt-1 text-sm">
                    {typeof value === "object" && value !== null
                      ? Object.values(value).filter(Boolean).join(", ")
                      : value || "-"}
                  </p>
                </div>
              ))}

            {/* Present Address */}
            <div className="col-span-3">
              <label className="text-sm font-medium text-muted-foreground">
                Present Address
              </label>
              <p className="mt-1 text-sm">{presentAddress}</p>
            </div>

            {/* Permanent Address */}
            <div className="col-span-3">
              <label className="text-sm font-medium text-muted-foreground">
                Permanent Address
              </label>
              <p className="mt-1 text-sm">{permanentAddress}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default withResourceAccess(ContactPage, {
  resource: Resources.STAFFS,
});

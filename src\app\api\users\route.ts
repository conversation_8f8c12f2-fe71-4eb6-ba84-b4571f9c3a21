import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const users = await prisma.user.findMany({
      include: {
        userRoles: {
          select: {
            id: true,
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
                rolePermissions: {
                  select: {
                    id: true,
                    permission: {
                      select: {
                        id: true,
                        name: true,
                        description: true,
                        resource: true,
                        action: true,
                      },
                    },
                  },
                },
              },
            }
          },
        },
      },
    });

    const transformedUsers = users.map((user) => ({
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      school: user.school,
      isActive: user.isActive,
      roles: user.userRoles.map((role) => ({
        id: role.role.id,
        name: role.role.name,
        displayName: role.role.displayName,
      })),
      permissions: user.userRoles
        .map((role) => role.role.rolePermissions)
        .flat()
        .map((permission) => permission.permission.name),
    }));

    return NextResponse.json(transformedUsers);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}
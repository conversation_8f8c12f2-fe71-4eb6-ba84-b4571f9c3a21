"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Shield,
  Users,
} from "lucide-react";
import CustomDialog from "@/components/shared/CustomDialog";
import { ROLES } from "@/lib/permissions";
import { Role } from "@/lib/types";
import { Separator } from "@/components/ui/separator";

export function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>(ROLES);
  const [newRole, setNewRole] = useState({ name: "", description: "" });

  const handleDeleteRole = (roleId: string) => {
    setRoles(roles.filter((role) => role.id !== roleId));
  };

  const getRoleTypeColor = (isSystem: boolean) => {
    return isSystem
      ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground">
            Role Management
          </h2>
          <p className="text-muted-foreground">
            Create and manage user roles for your school system
          </p>
        </div>
        <CustomDialog
          title="Create New Role"
          description="Define a new role with specific permissions for your school management system."
          trigger={
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Role
            </Button>
          }
        >
          <RoleForm
            newRole={newRole}
            setNewRole={setNewRole}
            roles={roles}
            setRoles={setRoles}
            type="create"
          />
        </CustomDialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Roles
          </CardTitle>
          <CardDescription>
            Manage all roles and their associated permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Role Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="w-[50px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">
                    {role.displayName}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {role.description}
                  </TableCell>
                  <TableCell>
                    <Badge className={getRoleTypeColor(role.isSystem)}>
                      {role.isSystem ? "System" : "Custom"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      {role.userCount}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {role.permissions.length} permissions
                    </Badge>
                  </TableCell>
                  <TableCell>{role.createdAt}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <CustomDialog
                            title="Edit Role"
                            description="Modify the role's name, description, and permissions."
                            asChild={false}
                            trigger={
                              <span className="flex items-center gap-1">
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Role
                              </span>
                            }
                          >
                            <RoleForm
                              newRole={role}
                              setNewRole={setNewRole}
                              roles={roles}
                              setRoles={setRoles}
                              type="edit"
                            />
                          </CustomDialog>
                        </DropdownMenuItem>
                        <Separator />
                        {!role.isSystem && (
                          <DropdownMenuItem
                            onClick={() => handleDeleteRole(role.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete Role
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

function RoleForm({
  newRole,
  setNewRole,
  roles,
  setRoles,
  type,
}: {
  newRole: { name: string; description: string };
  setNewRole: (role: { name: string; description: string }) => void;
  roles: Role[];
  setRoles: (roles: Role[]) => void;
  type: "create" | "edit";
}) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newRole.name && newRole.description) {
      const role: Role = {
        id: Date.now().toString(),
        name: newRole.name.toLocaleLowerCase(),
        displayName: newRole.name,
        description: newRole.description,
        userCount: 0,
        permissions: [],
        createdAt: new Date().toISOString().split("T")[0],
        isSystem: false,
        isActive: true,
        updatedAt: new Date().toISOString().split("T")[0],
      };
      setRoles([...roles, role]);
      setNewRole({ name: "", description: "" });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="roleName">Role Name</Label>
        <Input
          id="roleName"
          value={newRole.name}
          onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
          placeholder="e.g., Vice Principal, Librarian"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="roleDescription">Description</Label>
        <Textarea
          id="roleDescription"
          value={newRole.description}
          onChange={(e) =>
            setNewRole({ ...newRole, description: e.target.value })
          }
          placeholder="Describe the role's responsibilities and access level"
        />
      </div>
      <div className="flex gap-4 pt-4">
        <Button type="submit">
          {type === "create" ? "Create Role" : "Update Role"}
        </Button>
      </div>
    </form>
  );
}

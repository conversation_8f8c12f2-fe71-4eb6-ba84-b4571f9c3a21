"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Shield,
  Users,
} from "lucide-react";
import CustomDialog from "@/components/shared/CustomDialog";
import type { Role } from "@/lib/types";
import { Separator } from "@/components/ui/separator";
import {
  createRole,
  deleteRole,
  updateRole,
} from "@/lib/server/roles/roles.api";
import { showErrorToast } from "@/lib/toast-utils";

export function RoleManagement({ roles }: { roles: Role[] }) {
  const handleDeleteRole = async (roleId: string) => {
    await deleteRole(roleId);
  };

  const getRoleTypeColor = (isSystem: boolean) => {
    return isSystem
      ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground">
            Role Management
          </h2>
          <p className="text-muted-foreground">
            Create and manage user roles for your school system
          </p>
        </div>
        <CustomDialog
          title="Create New Role"
          description="Define a new role with specific permissions for your school management system."
          trigger={
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Role
            </Button>
          }
        >
          <RoleForm />
        </CustomDialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Roles
          </CardTitle>
          <CardDescription>
            Manage all roles and their associated permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Role Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="w-[50px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">
                    {role.displayName}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {role.description}
                  </TableCell>
                  <TableCell>
                    <Badge className={getRoleTypeColor(role.isSystem)}>
                      {role.isSystem ? "System" : "Custom"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      {role.userCount}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {role.permissions.length} permissions
                    </Badge>
                  </TableCell>
                  <TableCell>{role.createdAt}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <CustomDialog
                            title="Edit Role"
                            description="Modify the role's name, description, and permissions."
                            asChild={false}
                            trigger={
                              <span className="flex items-center gap-1">
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Role
                              </span>
                            }
                          >
                            <RoleForm
                              role={{
                                name: role.name,
                                displayName: role.displayName,
                                description: role.description,
                              }}
                              roleId={role.id}
                            />
                          </CustomDialog>
                        </DropdownMenuItem>
                        <Separator />
                        {!role.isSystem && (
                          <DropdownMenuItem
                            onClick={() => handleDeleteRole(role.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete Role
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

function RoleForm({
  role,
  roleId,
}: {
  role?: {
    name: string;
    displayName: string;
    description: string;
  };
  roleId?: string;
}) {
  const [formData, setFormData] = useState(
    role || {
      name: "",
      displayName: "",
      description: "",
    }
  );
  const [errors, setErrors] = useState({
    name: "",
    displayName: "",
    description: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.displayName || !formData.description) {
      setErrors({
        name: !formData.name ? "Name is required" : "",
        displayName: !formData.displayName ? "Display name is required" : "",
        description: !formData.description ? "Description is required" : "",
      });
      return;
    }

    try {
      const response = roleId
        ? await updateRole({
            id: roleId,
            name: formData.name.toLocaleLowerCase(),
            displayName: formData.displayName,
            description: formData.description,
          })
        : await createRole({
            ...formData,
            name: formData.name.toLocaleLowerCase(),
          });

      if (response.error) {
        console.error(response.error);
        return;
      }
    } catch (error) {
      console.error("Error:", error);
      showErrorToast("Failed to create role");
    } finally {
      setIsSubmitting(false);
    }

    setFormData({
      name: "",
      displayName: "",
      description: "",
    });
    setErrors({
      name: "",
      displayName: "",
      description: "",
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="roleName">Role Name</Label>
        <Input
          id="roleName"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="e.g., Vice Principal, Librarian"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="roleName">Display Name</Label>
        <Input
          id="roleDisplayName"
          value={formData.displayName}
          onChange={(e) =>
            setFormData({ ...formData, displayName: e.target.value })
          }
          placeholder="e.g., Vice Principal, Librarian"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="roleDescription">Description</Label>
        <Textarea
          id="roleDescription"
          value={formData.description}
          onChange={(e) =>
            setFormData({ ...formData, description: e.target.value })
          }
          placeholder="Describe the role's responsibilities and access level"
        />
      </div>
      <div className="flex gap-4 pt-4">
        <Button type="submit">{roleId ? "Create Role" : "Update Role"}</Button>
      </div>
    </form>
  );
}

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["kingdom_sis", "public"]
}

// Enums
enum StudentStatus {
  active
  inactive
  suspended
  graduated

  @@schema("kingdom_sis")
}

enum EnrollmentType {
  fulltime
  parttime
  online
  hybrid

  @@schema("kingdom_sis")
}

// Models
model User {
  id            String   @id @default(uuid())
  email         String   @unique
  passwordHash  String?  @map("password_hash")
  name          String
  avatar        String?
  school        String
  isActive      Boolean  @default(true)
  emailVerified <PERSON>olean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  userRoles UserRoleAssignment[]

  // Relations
  student Student?

  @@map("users")
  @@schema("kingdom_sis")
}

model School {
  id        String   @id @default(uuid())
  name      String
  code      String   @unique
  address   String?
  phone     String?
  email     String?
  website   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  departments Department[]
  students    Student[]

  @@map("schools")
  @@schema("kingdom_sis")
}

model Department {
  id          String   @id @default(uuid())
  schoolId    String   @map("school_id")
  name        String
  code        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  school   School    @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  students Student[]

  @@unique([schoolId, code])
  @@map("departments")
  @@schema("kingdom_sis")
}

model Student {
  id             String         @id @default(uuid())
  userId         String         @unique
  schoolId       String
  studentId      String         @unique
  status         StudentStatus  @default(active)
  enrollmentType EnrollmentType @default(fulltime)
  grade          String?
  year           Int?
  departmentId   String?
  enrollmentDate DateTime       @default(now())
  graduationDate DateTime?
  gpa            Decimal?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  school     School      @relation(fields: [schoolId], references: [id], onDelete: Cascade)
  department Department? @relation(fields: [departmentId], references: [id])

  @@map("students")
  @@schema("kingdom_sis")
}

model Role {
  id          String   @id @default(uuid())
  name        String   @unique
  displayName String
  description String?
  isSystem    Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  userRoles       UserRoleAssignment[]
  rolePermissions RolePermissionAssignment[]

  @@map("roles")
  @@schema("kingdom_sis")
}

model Permission {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  resource    String
  action      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermissionAssignment[]

  @@map("permissions")
  @@schema("kingdom_sis")
}

model RolePermissionAssignment {
  id           String     @id @default(uuid())
  roleId       String
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permissionId String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@index([roleId, permissionId])
  @@map("role_permissions_assignment")
  @@schema("kingdom_sis")
}

model UserRoleAssignment {
  id     String @id @default(uuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  roleId String
  role   Role   @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@index([userId, roleId])
  @@map("user_roles_assignment")
  @@schema("kingdom_sis")
}

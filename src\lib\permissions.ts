import { Context, User } from "./types";

export enum Resources {
  DASHBOARD = "dashboard",
  ACADEMICS = "academics",
  ACADEMIC_DEPARTMENTS = "academic_departments",
  CLASSES = "classes",
  USERS = "users",
  SESSIONS = "sessions",
  SUBJECTS = "subjects",
  TIMETABLES = "timetables",
  CERTIFICATES = "certificates",
  GRADES = "grades",
  ID_CARDS = "id_cards",
  STAFFS = "staffs",
  STAFF_DEPARTMENTS = "staff_departments",
  CONTACTS = "contacts",
  TEACHERS = "teachers",
  DESIGNATIONS = "designations",
  PAYROLLS = "payrolls",
  LEAVE = "leave",
  STUDENTS = "students",
  REGISTRATIONS = "registrations",
  HEALTH_RECORDS = "health_records",
  PROMOTIONS = "promotions",
  LEAVE_REQUESTS = "leave_requests",
  ATTENDANCE = "attendance",
  REPORTS = "reports",
  SETTINGS = "settings",
  ROLES = "roles",
  FEES = "fees",
  FEE_ALLOCATIONS = "fee_allocations",
  PAYMENT = "payment",
  ENROLLMENT_FORM = "enrollment_form",
  PRINTOUT = "printout",
  HELP = "help",
}

export enum Actions {
  CREATE = "create",
  READ = "read",
  UPDATE = "update",
  DELETE = "delete",
  MANAGE = "manage", // Full access
  TAKE = "take", // Take attendance
}

const ALL_PERMISSIONS = [
  { resource: Resources.DASHBOARD, action: Actions.READ, name: `${Resources.DASHBOARD}:${Actions.READ}`, description: `user can ${Actions.READ} ${Resources.DASHBOARD}` },
];

// Predefined system roles with permissions
export const SYSTEM_ROLES = {
  SUPER_ADMIN: {
    id: "1",
    name: "super_admin",
    displayName: "Super Admin",
    description: "Super Administrator with all permissions",
    userCount: 1,
    permissions: [
      { resource: "*", action: "manage" }, // Full access to everything
    ],
    isSystem: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  ADMIN: {
    id: "2",
    name: "admin",
    displayName: "School Admin",
    description: "School Administrator with limited permissions",
    userCount: 3,
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.STUDENTS, action: Actions.MANAGE },
      { resource: Resources.TEACHERS, action: Actions.MANAGE },
      { resource: Resources.CLASSES, action: Actions.MANAGE },
      { resource: Resources.GRADES, action: Actions.READ },
      { resource: Resources.ATTENDANCE, action: Actions.READ },
      { resource: Resources.REPORTS, action: Actions.READ },
      { resource: Resources.SETTINGS, action: Actions.MANAGE },
      { resource: Resources.USERS, action: Actions.MANAGE },
      { resource: Resources.ROLES, action: Actions.MANAGE },
    ],
    isSystem: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  PRINCIPAL: {
    id: "3",
    name: "principal",
    displayName: "Principal",
    description: "Principal with limited permissions",
    userCount: 1,
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.STUDENTS, action: Actions.MANAGE },
      { resource: Resources.TEACHERS, action: Actions.MANAGE },
      { resource: Resources.CLASSES, action: Actions.MANAGE },
      { resource: Resources.GRADES, action: Actions.READ },
      { resource: Resources.ATTENDANCE, action: Actions.READ },
      { resource: Resources.REPORTS, action: Actions.READ },
      { resource: Resources.SETTINGS, action: Actions.MANAGE },
      { resource: Resources.USERS, action: Actions.MANAGE },
      { resource: Resources.ROLES, action: Actions.MANAGE },
    ],
    isSystem: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  TEACHER: {
    id: "4",
    name: "teacher",
    displayName: "Teacher",
    description: "Teacher with classroom management permissions",
    userCount: 45,
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      { resource: Resources.STUDENTS, action: Actions.READ },
      {
        resource: Resources.CLASSES,
        action: Actions.READ,
        conditions: { ownClasses: true },
      },
      {
        resource: Resources.GRADES,
        action: Actions.MANAGE,
        conditions: { ownClasses: true },
      },
      {
        resource: Resources.ATTENDANCE,
        action: Actions.MANAGE,
        conditions: { ownClasses: true },
      },
      {
        resource: Resources.REPORTS,
        action: Actions.READ,
        conditions: { ownClasses: true },
      },
    ],
    isSystem: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  STUDENT: {
    id: "5",
    name: "student",
    displayName: "Student",
    description: "Student with limited view permissions",
    userCount: 180,
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      {
        resource: Resources.GRADES,
        action: Actions.READ,
        conditions: { ownRecords: true },
      },
      {
        resource: Resources.ATTENDANCE,
        action: Actions.READ,
        conditions: { ownRecords: true },
      },
      {
        resource: Resources.CLASSES,
        action: Actions.READ,
        conditions: { enrolledClasses: true },
      },
    ],
    isSystem: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  PARENT: {
    id: "6",
    name: "parent",
    displayName: "Parent",
    description: "Parent with view permissions for their children",
    userCount: 120,
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      {
        resource: Resources.STUDENTS,
        action: Actions.READ,
        conditions: { ownChildren: true },
      },
      {
        resource: Resources.GRADES,
        action: Actions.READ,
        conditions: { ownChildren: true },
      },
      {
        resource: Resources.ATTENDANCE,
        action: Actions.READ,
        conditions: { ownChildren: true },
      },
    ],
    isSystem: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  APPLICANT: {
    id: "7",
    name: "applicant",
    displayName: "Applicant",
    description: "Applicant with limited view permissions",
    userCount: 10,
    permissions: [
      { resource: Resources.DASHBOARD, action: Actions.READ },
      {
        resource: Resources.ENROLLMENT_FORM,
        action: Actions.READ,
        conditions: { ownRecords: true },
      },
      { resource: Resources.ENROLLMENT_FORM, action: Actions.CREATE },
      {
        resource: Resources.ENROLLMENT_FORM,
        action: Actions.UPDATE,
        conditions: { ownRecords: true },
      },
      {
        resource: Resources.PRINTOUT,
        action: Actions.READ,
        conditions: { ownRecords: true },
      },
      {
        resource: Resources.PAYMENT,
        action: Actions.READ,
        conditions: { ownRecords: true },
      },
      { resource: Resources.HELP, action: Actions.READ },
    ],
    isSystem: true,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
};

export const ROLES = [
  SYSTEM_ROLES.SUPER_ADMIN,
  SYSTEM_ROLES.ADMIN,
  SYSTEM_ROLES.PRINCIPAL,
  SYSTEM_ROLES.TEACHER,
  SYSTEM_ROLES.STUDENT,
  SYSTEM_ROLES.PARENT,
  SYSTEM_ROLES.APPLICANT,
]

export class PermissionChecker {
  private user: User;

  constructor(user: User) {
    this.user = user;
  }

  hasPermission(resource: string, action: string, context?: Context): boolean {
    // Super admin has access to everything
    if (this.user.roles.some((role) => role.name === "super_admin")) {
      return true;
    }

    // Check user's permissions
    const hasPermission = this.user.permissions.some((permission) => {
      // Check for wildcard permissions
      if (permission.resource === "*" && permission.action === "manage") {
        return true;
      }

      // Check exact match
      if (
        permission.resource === resource &&
        (permission.action === action || permission.action === "manage")
      ) {
        // Check additional conditions if present
        if (permission.conditions && context) {
          return this.checkConditions(permission.conditions, context);
        }

        return true;
      }

      return false;
    });

    return hasPermission;
  }

  private checkConditions(
    conditions: Record<string, boolean>,
    context: Context,
  ): boolean {
    // Implement condition checking logic
    // Examples:
    // - ownRecords: user can only access their own records
    // - ownChildren: parent can only access their children's records
    // - ownClasses: teacher can only access classes they teach

    if (conditions.ownRecords && context.userId !== this.user.id) {
      return false;
    }

    if (conditions.ownChildren && context.parentId !== this.user.id) {
      return false;
    }

    if (conditions.ownClasses && !context.teacherIds?.includes(this.user.id)) {
      return false;
    }

    return true;
  }

  canAccess(
    resource: string,
    action: string = Actions.READ,
    context?: Context,
  ): boolean {
    return this.hasPermission(resource, action, context);
  }
}

// Helper function to check permissions
export function checkPermission(
  user: User,
  resource: string,
  action: string = Actions.READ,
  context?: Context,
): boolean {
  const checker = new PermissionChecker(user);
  return checker.hasPermission(resource, action, context);
}

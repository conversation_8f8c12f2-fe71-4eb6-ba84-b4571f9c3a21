import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as { userID: string; roleIds: string[] };

    await prisma.$transaction(async (tx) => {
      await tx.userRoleAssignment.deleteMany({
        where: { userId: body.userID },
      });
      await tx.userRoleAssignment.createMany({
        data: body.roleIds.map((roleId: string) => ({
          userId: body.userID,
          roleId,
        })),
      });
    });
    
    return NextResponse.json({ success: true, message: "Role assigned" });
  } catch (error) {
    console.error("Error assigning role:", error);
    return NextResponse.json(
      { error: "Failed to assign role" },
      { status: 500 },
    );
  }
}
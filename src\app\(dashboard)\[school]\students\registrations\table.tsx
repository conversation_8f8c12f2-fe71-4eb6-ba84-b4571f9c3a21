"use client";

import { DataTable } from "@/components/shared/data-table/data-table";
import {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { columns } from "./columns";
import { filterConfigs } from "./filtering";
import { registrationBulkActions } from "./bulk-actions";

export default function StudentRegistrationTable() {
  const [data, setData] = useState<Registrations>([]);
  const [loading, setLoading] = useState(true);
  const [pageCount, setPageCount] = useState(-1);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  async function fetchRegistrations(params: {
    pagination: PaginationState;
    sorting: SortingState;
    columnFilters: ColumnFiltersState;
  }): Promise<{ data: Registrations; pageCount: number; total: number }> {
    const result = await getRegistrations(params);

    return {
      data: result.data,
      pageCount: result.pageCount,
      total: result.total,
    };
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchRegistrations({
          pagination,
          sorting,
          columnFilters,
        });
        setData(result.data);
        setPageCount(result.pageCount);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [pagination, sorting, columnFilters]);

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onSortingChange={setSorting}
        onColumnFiltersChange={setColumnFilters}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        filterConfigs={filterConfigs}
        searchColumn="name"
        searchPlaceholder="Search registrations by name..."
        enableBulkActions={true}
        bulkActions={registrationBulkActions}
      />
    </>
  );
}

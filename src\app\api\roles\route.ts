import prisma from "@/lib/prisma";
import { Role } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

interface CreateRolePayload {
  id?: string;
  name: string;
  displayName: string;
  description: string;
}

export async function GET(request: NextRequest) {
  try {
    const roles = await prisma.role.findMany({
      include: {
        rolePermissions: {
          select: {
            id: true,
            permission: {
              select: {
                id: true,
                name: true,
                description: true,
                resource: true,
                action: true,
              },
            }
          },
        },
        _count: {
          select: { userRoles: true },
        },
      },
    });

    const transformedRoles = roles.map((role) => ({
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description || undefined,
      isActive: role.isActive,
      isSystem: role.isSystem,
      userCount: role._count.userRoles,
      permissions: role.rolePermissions.map((permission) => permission.permission),
      createdAt: role.createdAt.toLocaleString(),
      updatedAt: role.updatedAt.toLocaleString(),
    })) as Role[]
    
    return NextResponse.json(transformedRoles);
  } catch (error) {
    console.error("Error fetching roles:", error);
    return NextResponse.json(
      { error: "Failed to fetch roles" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CreateRolePayload;
    const role = await prisma.role.create({
      data: body,
    });
    return NextResponse.json(role);
  } catch (error) {
    console.error("Error creating role:", error);
    return NextResponse.json(
      { error: "Failed to create role" },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json() as Partial<CreateRolePayload>;
    const role = await prisma.role.update({
      where: { id: body.id },
      data: body,
    });
    return NextResponse.json(role);
  } catch (error) {
    console.error("Error updating role:", error);
    return NextResponse.json(
      { error: "Failed to update role" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json() as { id: string };
    const role = await prisma.role.delete({ where: { id: body.id } });
    return NextResponse.json(role);
  } catch (error) {
    console.error("Error deleting role:", error);
    return NextResponse.json(
      { error: "Failed to delete role" },
      { status: 500 },
    );
  }
}

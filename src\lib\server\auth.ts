import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { Actions, checkPermission } from "@/lib/permissions";
import { Context, User } from "../types";
import { auth } from "@/lib/auth";
import { getUser } from "./users/users.api";

type School = {
  id: string;
  name: string;
  slug: string;
};

interface AuthResult {
  user: User | null;
  school: School | null;
  isAuthenticated: boolean;
}

/**
 * Get current user from server-side cookies
 * This runs on the server and validates tokens with your backend
 */
export async function getServerAuth(): Promise<AuthResult> {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get("accessToken")?.value;
  const refreshToken = cookieStore.get("refreshToken")?.value;

  if (!accessToken && !refreshToken) {
    return { user: null, school: null, isAuthenticated: false };
  }

  try {
    // Verify token with your Fastify backend
    const response = await fetch(`${process.env.BACKEND_URL}/auth/verify`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Cookie: `refreshToken=${refreshToken}`,
      },
    });

    if (!response.ok) {
      // Try to refresh token
      const refreshResponse = await fetch(
        `${process.env.BACKEND_URL}/auth/refresh`,
        {
          method: "POST",
          headers: {
            Cookie: `refreshToken=${refreshToken}`,
          },
        },
      );

      if (!refreshResponse.ok) {
        return { user: null, school: null, isAuthenticated: false };
      }

      const refreshData = await refreshResponse.json();

      // Update cookies with new tokens
      cookieStore.set("accessToken", refreshData.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 15 * 60, // 15 minutes
      });

      return {
        user: refreshData.user,
        school: refreshData.school,
        isAuthenticated: true,
      };
    }

    const data = await response.json();
    return {
      user: data.user,
      school: data.school,
      isAuthenticated: true,
    };
  } catch (error) {
    console.error("Server auth verification failed:", error);
    return { user: null, school: null, isAuthenticated: false };
  }
}

/**
 * Require authentication - redirect to login if not authenticated
 */
export async function requireAuth(
  schoolSlug?: string,
  redirectPath?: string,
): Promise<User> {
  const session = await auth();

  if (!session || !session.user) {
    const loginUrl = `/login${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ""}${schoolSlug ? `&school=${schoolSlug}` : ""}`;
    redirect(loginUrl);
  }

  const userData = await getUser(session.user.id) as User;

  return userData;
}

/**
 * Require specific school access
 */
export async function requireSchoolAccess(
  schoolSlug: string,
  redirectPath?: string,
): Promise<User> {
  const user = await requireAuth(schoolSlug, redirectPath);

  if (!user.school || user.school !== schoolSlug) {
    redirect("/unauthorized?reason=school_access");
  }

  return user;
}

/**
 * Check if user has specific permission
 */
export function hasServerPermission(
  user: User,
  resource: string,
  action: string = Actions.READ,
  context?: Context,
): boolean {
  return checkPermission(user, resource, action, context);
}

/**
 * Require specific permission - redirect if not authorized
 */
export async function requirePermission(
  user: User,
  resource: string,
  action: string = Actions.READ,
  context?: Context,
  redirectPath?: string,
): Promise<void> {
  if (!hasServerPermission(user, resource, action, context)) {
    const unauthorizedUrl = `/unauthorized?reason=permission&resource=${resource}&action=${action}`;
    redirect(redirectPath || unauthorizedUrl);
  }
}

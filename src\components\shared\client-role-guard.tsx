"use client";

import { ReactNode, useEffect, useState } from "react";
import { Actions } from "@/lib/permissions";
import { usePermissions } from "@/hooks/use-permissions";
import { Context, User } from "@/lib/types";
import { useSession } from "next-auth/react";
import { getUser } from "@/lib/server/users/users.api";
import { cn } from "@/lib/utils";

interface ClientRoleGuardProps {
  children: ReactNode;
  resource: string;
  action?: string;
  context?: Context;
  fallback?: ReactNode;
  showLoading?: boolean;
  className?: string;
}

/**
 * Client-side component guard for dynamic UI elements
 * Use this for buttons, forms, and interactive elements that need real-time permission checking
 */
export function ClientRoleGuard({
  children,
  resource,
  action = Actions.READ,
  context,
  fallback = null,
  showLoading = false,
  className,
}: ClientRoleGuardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const { data: session, status } = useSession();
  const { hasPermission } = usePermissions(user as User);

  const sessionUser = session?.user as User;

  async function fetchUser() {
    const userData = await getUser(sessionUser.id);
    setIsLoading(false);
    setUser(userData);
  }

  useEffect(() => {
    if (status === "authenticated") {
      fetchUser();
    }
  }, [status]);

  if (isLoading && showLoading) {
    return <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>;
  }

  if (!user || isLoading) {
    return <>{fallback}</>;
  }

  const hasAccess = hasPermission(resource, action, context);

  return hasAccess ? (
    <div className={cn("", className)}>{children}</div>
  ) : (
    <>{fallback}</>
  );
}

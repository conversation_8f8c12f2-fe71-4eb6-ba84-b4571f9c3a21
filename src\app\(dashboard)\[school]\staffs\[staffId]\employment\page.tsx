import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";
import EmploymentTable from "./table";
import { EmploymentInfoForm } from "../../_components/forms/employment.form";

const EmploymentInfo = [
  {
    id: "1",
    startDate: new Date("2022-01-01"),
    endDate: new Date("2024-01-01"),
    department: { id: "1", name: "Administrative" },
    designation: { id: "3", name: "Manager" },
    employmentStatus: "permanent",
  },
  {
    id: "2",
    startDate: new Date("2024-01-01"),
    endDate: null,
    department: { id: "2", name: "Teaching" },
    designation: { id: "5", name: "Teacher" },
    employmentStatus: "contract",
  },
];

function EmploymentPage() {
  const renderNoData = () => {
    return (
      <NoDataPlaceholder
        title="Manage all Employee Employment Records"
        description="Keep the documents related to your staff's employment."
      >
        <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
          <CustomDialog
            title="Add Employment"
            description="Add a new employment for the staff"
            trigger={<Button>Add Employment</Button>}
          >
            <EmploymentInfoForm />
          </CustomDialog>
        </ClientRoleGuard>
      </NoDataPlaceholder>
    );
  };

  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Employment</h1>
        <div className="flex items-center gap-2">
          <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
            <CustomDialog
              title="Add Employment"
              description="Add a new employment for the staff"
              trigger={<Button>Add Employment</Button>}
            >
              <EmploymentInfoForm />
            </CustomDialog>
          </ClientRoleGuard>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {EmploymentInfo.length === 0 ? (
        renderNoData()
      ) : (
        <EmploymentTable initialData={EmploymentInfo} />
      )}
    </div>
  );
}

export default withResourceAccess(EmploymentPage, {
  resource: Resources.STAFFS,
});

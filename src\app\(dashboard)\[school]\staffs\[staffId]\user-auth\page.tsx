import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";
import { Card, CardContent } from "@/components/ui/card";
import { auth } from "@/lib/auth";
import { getUser } from "@/lib/server/users/users.api";
import { getRoleOptions } from "@/lib/server/roles/roles.api";
import { UserAuthForm } from "../../_components/forms/user-auth.form";
import { Badge } from "@/components/ui/badge";

async function UserAuthPage() {
  const session = await auth();
  const [user, roleOptions] = await Promise.all([
    getUser(session?.user.id as string),
    getRoleOptions(),
  ]);

  const userAuthData = {
    email: user.email,
    username: user.name,
    roles: user.roles.map((role) => role.id),
  };

  const getRoleColor = (roleName: string) => {
    const colors = {
      super_admin: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      admin:
        "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      principal:
        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
      teacher: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      "Department Head":
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      librarian:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      counselor:
        "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
      student: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
      parent:
        "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    };
    return (
      colors[roleName as keyof typeof colors] || "bg-gray-100 text-gray-800"
    );
  };

  return (
    <>
      <div className="flex justify-end items-center mb-4">
        <ClientRoleGuard
          resource={Resources.STAFFS}
          action={Actions.UPDATE}
          className="flex gap-2"
        >
          <CustomDialog
            title="User Auth"
            description="Edit user auth information of the staff"
            trigger={<Button variant="outline">Edit</Button>}
          >
            <UserAuthForm
              initialData={userAuthData}
              roleOptions={roleOptions}
            />
          </CustomDialog>
        </ClientRoleGuard>
      </div>

      <Card>
        <CardContent className="px-6 space-y-4">
          <div className="grid grid-cols-4 gap-4">
            {Object.entries(userAuthData)
              .slice(0, -1)
              .map(([key, value]) => (
                <div key={key}>
                  <label className="text-sm font-medium text-muted-foreground">
                    {key}
                  </label>
                  <p className="mt-1 text-sm">{value || "-"}</p>
                </div>
              ))}
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Roles
            </label>
            <div className="flex flex-wrap gap-2 mt-2">
              {user.roles.map((role) => (
                <Badge key={role.id} className={getRoleColor(role.name)}>
                  {role.name}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}

export default withResourceAccess(UserAuthPage, {
  resource: Resources.STAFFS,
});

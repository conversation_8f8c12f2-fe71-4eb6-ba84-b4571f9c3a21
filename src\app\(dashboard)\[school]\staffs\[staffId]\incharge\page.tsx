import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";
import InchargeTable from "./table";

const InchargeInfo = [
  {
    id: "1",
    startDate: new Date("2022-01-01"),
    endDate: new Date("2024-01-01"),
    details: "Details 1",
  },
  {
    id: "2",
    startDate: new Date("2024-01-01"),
    endDate: null,
    details: "Details 2",
  },
];

function InchargePage() {
  const renderNoData = () => {
    return (
      <NoDataPlaceholder
        title="Manage all Employee Incharge Records"
        description="Keep the documents related to your staff's incharge."
      >
        <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
          <CustomDialog
            title="Add Incharge"
            description="Add a new incharge for the staff"
            trigger={<Button>Add Incharge</Button>}
          >
            <div>Add Incharge Form</div>
          </CustomDialog>
        </ClientRoleGuard>
      </NoDataPlaceholder>
    );
  };

  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Incharge</h1>
        <Button variant="ghost" size="icon">
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>

      {InchargeInfo.length === 0 ? (
        renderNoData()
      ) : (
        <InchargeTable initialData={InchargeInfo} />
      )}
    </div>
  );
}

export default withResourceAccess(InchargePage, {
  resource: Resources.STAFFS,
});

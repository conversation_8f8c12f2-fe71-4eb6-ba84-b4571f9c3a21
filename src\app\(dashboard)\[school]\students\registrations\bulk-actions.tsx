import {
  Trash2,
  Edit,
  Mail,
  Download,
  Archive,
  UserCheck,
  UserX,
  GraduationCap,
  FileText,
  Send,
} from "lucide-react";
import { toast } from "sonner";

// Define the bulk actions for your student management system
export const registrationBulkActions = [
  {
    key: "activate",
    label: "Activate Students",
    icon: <UserCheck className="h-4 w-4" />,
    variant: "default" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Activating students:", selectedRows);
      // Here you would make an API call to activate the students
      // Example: await activateStudents(selectedRows.map(row => row.id));
      toast.success(`Activated ${selectedRows.length} students`);
    },
  },
  {
    key: "graduate",
    label: "Mark as Graduated",
    icon: <GraduationCap className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Graduating students:", selectedRows);
      // Example: await graduateStudents(selectedRows.map(row => row.id));
      toast.success(`Marked ${selectedRows.length} students as graduated`);
    },
  },
  {
    key: "send-notification",
    label: "Send Notification",
    icon: <Send className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Sending notification to students:", selectedRows);
      // Example: await sendNotification(selectedRows.map(row => row.id), notificationData);
      toast.info(`Sending notification to ${selectedRows.length} students`);
    },
  },
  {
    key: "send-email",
    label: "Send Email",
    icon: <Mail className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Sending email to students:", selectedRows);
      // Example: await sendBulkEmail(selectedRows.map(row => row.email), emailData);
      toast.info(`Sending email to ${selectedRows.length} students`);
    },
  },
  {
    key: "generate-report",
    label: "Generate Report",
    icon: <FileText className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Generating report for students:", selectedRows);
      // Example: await generateStudentReport(selectedRows.map(row => row.id));
      toast.info(`Generating report for ${selectedRows.length} students`);
    },
  },
  {
    key: "export",
    label: "Export to CSV",
    icon: <Download className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Exporting students:", selectedRows);

      // Create CSV content
      const headers = [
        "Name",
        "Email",
        "Status",
        "Grade",
        "Department",
        "Enrollment Type",
      ];
      const csvContent = [
        headers.join(","),
        ...selectedRows.map((student) =>
          [
            `"${student.name}"`,
            `"${student.email}"`,
            `"${student.status}"`,
            `"${student.grade}"`,
            `"${student.department}"`,
            `"${student.enrollmentType}"`,
          ].join(",")
        ),
      ].join("\n");

      // Download CSV file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `students-export-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      link.click();
      window.URL.revokeObjectURL(url);

      toast.success(`Exported ${selectedRows.length} students to CSV`);
    },
  },
  {
    key: "bulk-edit",
    label: "Bulk Edit",
    icon: <Edit className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Opening bulk edit for students:", selectedRows);
      // Example: openBulkEditModal(selectedRows);
      toast.info(`Opening bulk edit for ${selectedRows.length} students`);
    },
  },
  {
    key: "archive",
    label: "Archive Students",
    icon: <Archive className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Archiving students:", selectedRows);
      // Example: await archiveStudents(selectedRows.map(row => row.id));
      toast.info(`Archived ${selectedRows.length} students`);
    },
  },
  {
    key: "deactivate",
    label: "Deactivate Students",
    icon: <UserX className="h-4 w-4" />,
    variant: "outline" as const,
    onClick: (selectedRows: any[]) => {
      console.log("Deactivating students:", selectedRows);
      // Example: await deactivateStudents(selectedRows.map(row => row.id));
      toast.warning(`Deactivated ${selectedRows.length} students`);
    },
  },
  {
    key: "delete",
    label: "Delete Students",
    icon: <Trash2 className="h-4 w-4" />,
    variant: "destructive" as const,
    onClick: (selectedRows: any[]) => {
      const confirmed = confirm(
        `Are you sure you want to delete ${selectedRows.length} students? This action cannot be undone.`
      );

      if (confirmed) {
        console.log("Deleting students:", selectedRows);
        // Example: await deleteStudents(selectedRows.map(row => row.id));
        toast.error(`Deleted ${selectedRows.length} students`);
      }
    },
  },
];

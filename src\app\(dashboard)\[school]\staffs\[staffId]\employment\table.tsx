"use client";

import { DataTable } from "@/components/shared/data-table/data-table";
import React, { useEffect, useState } from "react";
import { columns } from "./columns";
import type { StaffEmployment } from "../../_types/type";

export default function EmploymentTable({
  initialData,
}: {
  initialData: StaffEmployment[];
}) {
  const [data, setData] = useState<StaffEmployment[]>([]);
  const [loading, setLoading] = useState(true);

  async function fetchDepartments(): Promise<{
    data: StaffEmployment[];
    total: number;
  }> {
    const result = {
      data: initialData,
      total: initialData.length,
    };

    return {
      data: result.data,
      total: result.total,
    };
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const result = await fetchDepartments();
        setData(result.data);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [initialData]);

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        showToolBar={false}
      />
    </>
  );
}

"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { toast } from "sonner";
import { FormInput, FormSelect, FormDatePicker } from "@/components/forms";
import z from "zod";
import { staffStatus } from "@/data/options";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { designationOptions, staffDepartmentOptions } from "@/lib/mock-data";

interface EmploymentInfoFormProps {
  initialData?: Partial<EmploymentInfoFormData>;
}

const employmentInfoSchema = z.object({
  id: z.string().optional(),
  startDate: z.date({
    error: "Start date is required",
  }),
  department: z.string().min(2, "Select a department"),
  designation: z.string().min(2, "Select a designation"),
  employmentStatus: z.string().min(2, "Select employment status"),
  remarks: z.string().optional(),
});

type EmploymentInfoFormData = z.infer<typeof employmentInfoSchema>;

export function EmploymentInfoForm({ initialData }: EmploymentInfoFormProps) {
  const form = useForm<EmploymentInfoFormData>({
    resolver: zodResolver(employmentInfoSchema),
    defaultValues: {
      startDate: new Date(),
      department: undefined,
      designation: undefined,
      employmentStatus: undefined,
      remarks: "",
      ...initialData,
    },
  });

  const handleSubmit = async (data: EmploymentInfoFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Employment Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormDatePicker
                  form={form}
                  name="startDate"
                  label="Start Date"
                  placeholder="Select start date"
                />
                <FormSelect
                  form={form}
                  name="department"
                  label="Department"
                  placeholder="Select department"
                  options={staffDepartmentOptions}
                />
                <FormSelect
                  form={form}
                  name="designation"
                  label="Designation"
                  placeholder="Select designation"
                  options={designationOptions}
                />
                <FormSelect
                  form={form}
                  name="employmentStatus"
                  label="Employment Status"
                  placeholder="Select employment status"
                  options={staffStatus}
                />
              </div>
              <FormInput
                form={form}
                name="remarks"
                label="Remarks"
                placeholder="Enter remarks"
              />
            </div>

            {/* Form Actions */}
            <div className="flex pt-4">
              <Button
                type="submit"
                className="flex-1 md:flex-none"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>Submit</>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

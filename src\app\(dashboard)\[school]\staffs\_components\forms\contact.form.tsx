"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { toast } from "sonner";
import {
  FormInput,
  FormSelect,
  FormRadioGroup,
  FormDatePicker,
  FormCheckbox,
} from "@/components/forms";
import z from "zod";
import { getNationality } from "@/data/getNationality";
import {
  bloodGroup,
  genderOptions,
  maritalStatus,
  religiousAffiliation,
  staffType,
  states,
} from "@/data/options";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface ContactInfoFormProps {
  initialData?: Partial<ContactInfoFormData>;
}

const contactInfoSchema = z.object({
  id: z.string().optional(),
  email: z.email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  alternateEmail: z.string().optional(),
  alternatePhone: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactNumber: z.string().optional(),
  emergencyContactRelation: z.string().optional(),
  presentAddress: z.object({
    address1: z.string().min(2, "Address must be at least 2 characters"),
    address2: z.string().optional(),
    barangay: z.string().min(2, "Barangay must be at least 2 characters"),
    city: z.string().min(2, "City must be at least 2 characters"),
    province: z.string().min(2, "Province must be at least 2 characters"),
    country: z.string().min(2, "Country must be at least 2 characters"),
    postalCode: z.string().min(2, "Postal code must be at least 2 characters"),
  }),
  permanentAddress: z.object({
    address1: z.string().optional(),
    address2: z.string().optional(),
    barangay: z.string().optional(),
    city: z.string().optional(),
    province: z.string().optional(),
    country: z.string().optional(),
    postalCode: z.string().optional(),
    sameAsPresentAddress: z.boolean(),
  }),
});

type ContactInfoFormData = z.infer<typeof contactInfoSchema>;

export function ContactInfoForm({ initialData }: ContactInfoFormProps) {
  const [nationalityData, setNationalityData] = useState<
    { label: string; value: string }[]
  >([]);

  const form = useForm<ContactInfoFormData>({
    resolver: zodResolver(contactInfoSchema),
    defaultValues: {
      email: "",
      phone: "",
      alternateEmail: "",
      alternatePhone: "",
      emergencyContactName: "",
      emergencyContactNumber: "",
      emergencyContactRelation: "",
      presentAddress: {
        address1: "",
        address2: "",
        barangay: "",
        city: "",
        province: "",
        country: "",
        postalCode: "",
      },
      permanentAddress: {
        address1: "",
        address2: "",
        barangay: "",
        city: "",
        province: "",
        country: "",
        postalCode: "",
        sameAsPresentAddress: true,
      },
      ...initialData,
    },
  });

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }

    const fetchNationality = async () => {
      try {
        const data = await getNationality();
        setNationalityData(data);
      } catch (error) {
        console.error("Failed to fetch nationality data:", error);
      }
    };
    fetchNationality();
  }, [initialData, form]);

  const handleSubmit = async (data: ContactInfoFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Contact Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="email"
                  label="Email Address"
                  type="email"
                  placeholder="Enter email address"
                />
                <FormInput
                  form={form}
                  name="phone"
                  label="Phone Number"
                  type="tel"
                  placeholder="Enter phone number"
                />
                <FormInput
                  form={form}
                  name="alternateEmail"
                  label="Alternate Email Address"
                  type="email"
                  placeholder="Enter alternate email address"
                />
                <FormInput
                  form={form}
                  name="alternatePhone"
                  label="Alternate Phone Number"
                  type="tel"
                  placeholder="Enter alternate phone number"
                />
                <FormInput
                  form={form}
                  name="emergencyContactName"
                  label="Emergency Contact Name"
                  placeholder="Enter emergency contact name"
                />
                <FormInput
                  form={form}
                  name="emergencyContactNumber"
                  label="Emergency Contact Number"
                  type="tel"
                  placeholder="Enter emergency contact number"
                />
                <FormInput
                  form={form}
                  name="emergencyContactRelation"
                  label="Emergency Contact Relation"
                  placeholder="Enter emergency contact relation"
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Present Address</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormInput
                    form={form}
                    name="presentAddress.address1"
                    label="Present Address - Address 1"
                    placeholder="Enter present address"
                  />
                  <FormInput
                    form={form}
                    name="presentAddress.address2"
                    label="Present Address - Address 2"
                    placeholder="Enter present address"
                  />
                  <FormInput
                    form={form}
                    name="presentAddress.barangay"
                    label="Present Address - Barangay"
                    placeholder="Enter present address"
                  />
                  <FormSelect
                    form={form}
                    name="presentAddress.city"
                    label="Present Address - City"
                    placeholder="Enter present address"
                    options={nationalityData}
                  />
                  <FormInput
                    form={form}
                    name="presentAddress.province"
                    label="Present Address - Province"
                    placeholder="Enter present address"
                  />
                  <FormInput
                    form={form}
                    name="presentAddress.country"
                    label="Present Address - Country"
                    placeholder="Enter present address"
                  />
                  <FormInput
                    form={form}
                    name="presentAddress.postalCode"
                    label="Present Address - Postal Code"
                    placeholder="Enter present address"
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Permanent Address</h3>
                <FormCheckbox
                  form={form}
                  name="permanentAddress.sameAsPresentAddress"
                  label="Same as present address"
                  description="Check if permanent address is the same as present address"
                />
                {!form.watch("permanentAddress.sameAsPresentAddress") && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormInput
                      form={form}
                      name="permanentAddress.address1"
                      label="Permanent Address - Address 1"
                      placeholder="Enter permanent address"
                    />
                    <FormInput
                      form={form}
                      name="permanentAddress.address2"
                      label="Permanent Address - Address 2"
                      placeholder="Enter permanent address"
                    />
                    <FormInput
                      form={form}
                      name="permanentAddress.barangay"
                      label="Permanent Address - Barangay"
                      placeholder="Enter permanent address"
                    />
                    <FormSelect
                      form={form}
                      name="permanentAddress.city"
                      label="Permanent Address - City"
                      placeholder="Enter permanent address"
                      options={nationalityData}
                    />
                    <FormInput
                      form={form}
                      name="permanentAddress.province"
                      label="Permanent Address - Province"
                      placeholder="Enter permanent address"
                    />
                    <FormInput
                      form={form}
                      name="permanentAddress.country"
                      label="Permanent Address - Country"
                      placeholder="Enter permanent address"
                    />
                    <FormInput
                      form={form}
                      name="permanentAddress.postalCode"
                      label="Permanent Address - Postal Code"
                      placeholder="Enter permanent address"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex pt-4">
              <Button
                type="submit"
                className="flex-1 md:flex-none"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>Submit</>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

import { But<PERSON> } from "@/components/ui/button";
import StudentRegistrationTable from "./table";
import { Plus } from "lucide-react";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { Actions, Resources } from "@/lib/permissions";
import PageWrapper from "../../_components/layouts/PageWrapper";
import { withResourceAccess } from "@/components/shared/page-gurad";

const breadcrumbItems = [
  { label: "Home", href: "/" },
  { label: "Students", href: "/students" },
  { label: "Registration" },
];

interface Registration {
  id: string;
  registrationNumber: string;
  addmissionNumber: string;
  name: string;
  phone: string;
  guardianName: string;
  guardianPhone: string;
  dateOfBirth: Date;
  grade: string;
  enrollmentType: string;
  registrationStage: string;
  status: string;
  registrationDate: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

function StudentRegistrationPage() {
  const renderButton = () => {
    return (
      <ClientRoleGuard resource={Resources.STUDENTS} action={Actions.CREATE}>
        <Button>
          <Plus /> Add Registration
        </Button>
      </ClientRoleGuard>
    );
  };

  return (
    <PageWrapper
      pgTitle="Manage Registrations"
      pgDescription="Manage registration records and information"
      breadcrumbItems={breadcrumbItems}
      headerButton={renderButton()}
      school="kingdom"
    >
      <StudentRegistrationTable />
    </PageWrapper>
  );
}

export default withResourceAccess(StudentRegistrationPage, {
  resource: Resources.STUDENTS,
});

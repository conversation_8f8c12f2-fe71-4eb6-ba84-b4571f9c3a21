import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";
import WorkShiftTable from "./table";
import { WorkShiftInfoForm } from "../../_components/forms/work-shift.form";

const WorkShiftInfo = [
  {
    id: "1",
    startDate: new Date("2022-01-01"),
    endDate: new Date("2024-01-01"),
    workShift: "Morning",
    remarks: "Remarks 1",
  },
  {
    id: "2",
    startDate: new Date("2024-01-01"),
    endDate: null,
    workShift: "Evening",
    remarks: "Remarks 2",
  },
];

function WorkShiftPage() {
  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Work Shift</h1>
        <div className="flex items-center gap-2">
          <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
            <CustomDialog
              title="Add WorkShift"
              description="Add a new work shift for the staff"
              trigger={<Button>Add WorkShift</Button>}
            >
              <WorkShiftInfoForm />
            </CustomDialog>
          </ClientRoleGuard>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {WorkShiftInfo.length === 0 ? (
        <NoDataPlaceholder
          title="Manage all Employee Work Shift Records"
          description="Keep the workShifts related to your staff's work shift."
        >
          <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
            <CustomDialog
              title="Add WorkShift"
              description="Add a new work shift for the staff"
              trigger={<Button>Add WorkShift</Button>}
            >
              <WorkShiftInfoForm />
            </CustomDialog>
          </ClientRoleGuard>
        </NoDataPlaceholder>
      ) : (
        <WorkShiftTable initialData={WorkShiftInfo} />
      )}
    </div>
  );
}

export default withResourceAccess(WorkShiftPage, {
  resource: Resources.STAFFS,
});

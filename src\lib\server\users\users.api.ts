import { ENV } from "@/config/env";
import { User } from "@/lib/types";

export async function getUsers() {
  const response = await fetch(`${ENV.BACKEND_URL}/api/users`, {
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to fetch users");
  }

  return response.json();
}

export async function getUser(userID: string) {
  const response = await fetch(
    `${ENV.BACKEND_URL}/api/users/user`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userID }),
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch user");
  }

  return response.json() as Promise<User>;
}

export async function login(email: string) {
  const response = await fetch(
    `${ENV.BACKEND_URL}/api/users/login`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to login");
  }

  return response.json() as Promise<User>;
}

import { Loader2 } from "lucide-react";
import { redirect } from "next/navigation";

export default async function StaffPage({
  params,
}: {
  params: Promise<Record<string, string>>;
}) {
  const { school, staffId } = await params;

  if (school || staffId) {
    redirect(`/${school}/staffs/${staffId}/basic`);
  }

  return (
    <div className="flex justify-center items-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

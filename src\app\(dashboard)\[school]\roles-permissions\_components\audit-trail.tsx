"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  History,
  Search,
  Download,
  Shield,
  Users,
  Settings,
} from "lucide-react";

interface AuditLog {
  id: string;
  timestamp: string;
  user: {
    name: string;
    email: string;
    avatar?: string;
  };
  action: string;
  resource: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  status: "success" | "failed" | "warning";
}

const auditLogs: AuditLog[] = [
  {
    id: "1",
    timestamp: "2024-03-15T10:30:00Z",
    user: {
      name: "<PERSON>. <PERSON>",
      email: "<EMAIL>",
    },
    action: "Role Assignment",
    resource: "User: Michael Chen",
    details: 'Assigned "Department Head" role to <PERSON> <PERSON>',
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    status: "success",
  },
  {
    id: "2",
    timestamp: "2024-03-15T09:45:00Z",
    user: {
      name: "Admin System",
      email: "<EMAIL>",
    },
    action: "Permission Update",
    resource: "Role: Teacher",
    details: 'Added "assignments:manage" permission to Teacher role',
    ipAddress: "127.0.0.1",
    userAgent: "System Process",
    status: "success",
  },
  {
    id: "3",
    timestamp: "2024-03-15T09:15:00Z",
    user: {
      name: "Michael Chen",
      email: "<EMAIL>",
    },
    action: "Login Attempt",
    resource: "Authentication",
    details: "Failed login attempt - incorrect password",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
    status: "failed",
  },
  {
    id: "4",
    timestamp: "2024-03-15T08:30:00Z",
    user: {
      name: "Emily Rodriguez",
      email: "<EMAIL>",
    },
    action: "Role Creation",
    resource: "Role: Substitute Teacher",
    details: 'Created new role "Substitute Teacher" with limited permissions',
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    status: "success",
  },
  {
    id: "5",
    timestamp: "2024-03-15T08:00:00Z",
    user: {
      name: "Dr. Sarah Johnson",
      email: "<EMAIL>",
    },
    action: "Permission Revoked",
    resource: "User: David Kim",
    details: 'Revoked "users:delete" permission from David Kim',
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    status: "warning",
  },
  {
    id: "6",
    timestamp: "2024-03-14T16:45:00Z",
    user: {
      name: "Lisa Thompson",
      email: "<EMAIL>",
    },
    action: "Role Update",
    resource: "Role: Librarian",
    details: "Updated Librarian role description and permissions",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X)",
    status: "success",
  },
];

export function AuditTrail() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterAction, setFilterAction] = useState("All Actions");
  const [filterStatus, setFilterStatus] = useState("All Status");

  const filteredLogs = auditLogs.filter((log) => {
    const matchesSearch =
      log.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesAction =
      filterAction === "All Actions" || log.action.includes(filterAction);
    const matchesStatus =
      filterStatus === "All Status" || log.status === filterStatus;

    return matchesSearch && matchesAction && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    const colors = {
      success:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      failed: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      warning:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const getActionIcon = (action: string) => {
    if (action.includes("Role")) return Shield;
    if (action.includes("User") || action.includes("Login")) return Users;
    return Settings;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Audit Trail</h2>
          <p className="text-muted-foreground">
            Track all role and permission changes in the system
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export Logs
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search audit logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterAction} onValueChange={setFilterAction}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Actions">All Actions</SelectItem>
                <SelectItem value="Role">Role Actions</SelectItem>
                <SelectItem value="Permission">Permission Actions</SelectItem>
                <SelectItem value="Login">Login Actions</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Status">All Status</SelectItem>
                <SelectItem value="success">Success</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            System Activity Log
          </CardTitle>
          <CardDescription>
            Comprehensive log of all role and permission changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Resource</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>IP Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.map((log) => {
                const ActionIcon = getActionIcon(log.action);
                return (
                  <TableRow key={log.id}>
                    <TableCell className="font-mono text-sm">
                      {formatTimestamp(log.timestamp)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage
                            src={log.user.avatar || "/placeholder.svg"}
                          />
                          <AvatarFallback className="text-xs">
                            {log.user.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">
                            {log.user.name}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {log.user.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <ActionIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{log.action}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {log.resource}
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {log.details}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(log.status)}>
                        {log.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {log.ipAddress}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

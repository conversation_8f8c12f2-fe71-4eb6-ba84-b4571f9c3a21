"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { toast } from "sonner";
import { FormInput, FormMultiSelect, FormCheckbox } from "@/components/forms";
import z from "zod";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface UserAuthFormProps {
  initialData?: Partial<UserAuthFormData>;
  roleOptions: { label: string; value: string }[];
}

const userAuthSchema = z.object({
  id: z.string().optional(),
  email: z.email("Invalid email address"),
  username: z.string().min(2, "Username must be at least 2 characters"),
  roles: z.array(z.string()).min(1, "Select at least one role"),
  changePassword: z.boolean().optional(),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type UserAuthFormData = z.infer<typeof userAuthSchema>;

export function UserAuthForm({ initialData, roleOptions }: UserAuthFormProps) {
  const form = useForm<UserAuthFormData>({
    resolver: zodResolver(userAuthSchema),
    defaultValues: {
      email: "",
      username: "",
      roles: [],
      changePassword: false,
      password: "",
      ...initialData,
    },
  });

  const handleSubmit = async (data: UserAuthFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* UserAuth Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="email"
                  label="Email Address"
                  type="email"
                  placeholder="Enter email address"
                />
                <FormInput
                  form={form}
                  name="username"
                  label="Username"
                  placeholder="Enter username"
                />
                <FormMultiSelect
                  form={form}
                  name="roles"
                  label="Roles"
                  placeholder="Select roles"
                  options={roleOptions}
                  searchPlaceholder="Search roles..."
                  maxSelected={3}
                />
              </div>

              <Separator />

              <div className="flex flex-col gap-4">
                <FormCheckbox
                  form={form}
                  name="changePassword"
                  label="Change Password"
                  description="Check if you want to change password"
                />
                {form.watch("changePassword") && (
                  <FormInput
                    form={form}
                    name="password"
                    label="Password"
                    type="password"
                    placeholder="Enter password"
                  />
                )}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex pt-4">
              <Button
                type="submit"
                className="flex-1 md:flex-none"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>Submit</>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

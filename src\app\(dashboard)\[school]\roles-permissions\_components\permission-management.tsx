"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Settings,
  Shield,
  Users,
  BookOpen,
  Calendar,
  FileText,
  BarChart3,
  Search,
} from "lucide-react";

interface Permission {
  id: string;
  resource: string;
  action: string;
  description: string;
  category: string;
}

interface Resource {
  name: string;
  icon: any;
  permissions: Permission[];
}

const resources: Resource[] = [
  {
    name: "Users",
    icon: Users,
    permissions: [
      {
        id: "1",
        resource: "users",
        action: "create",
        description: "Create new user accounts",
        category: "User Management",
      },
      {
        id: "2",
        resource: "users",
        action: "read",
        description: "View user information",
        category: "User Management",
      },
      {
        id: "3",
        resource: "users",
        action: "update",
        description: "Edit user profiles",
        category: "User Management",
      },
      {
        id: "4",
        resource: "users",
        action: "delete",
        description: "Delete user accounts",
        category: "User Management",
      },
    ],
  },
  {
    name: "Students",
    icon: BookOpen,
    permissions: [
      {
        id: "5",
        resource: "students",
        action: "create",
        description: "Register new students",
        category: "Student Management",
      },
      {
        id: "6",
        resource: "students",
        action: "read",
        description: "View student information",
        category: "Student Management",
      },
      {
        id: "7",
        resource: "students",
        action: "update",
        description: "Edit student records",
        category: "Student Management",
      },
      {
        id: "8",
        resource: "students",
        action: "delete",
        description: "Remove student records",
        category: "Student Management",
      },
    ],
  },
  {
    name: "Teachers",
    icon: Users,
    permissions: [
      {
        id: "9",
        resource: "teachers",
        action: "create",
        description: "Add new teachers",
        category: "Staff Management",
      },
      {
        id: "10",
        resource: "teachers",
        action: "read",
        description: "View teacher information",
        category: "Staff Management",
      },
      {
        id: "11",
        resource: "teachers",
        action: "update",
        description: "Edit teacher profiles",
        category: "Staff Management",
      },
      {
        id: "12",
        resource: "teachers",
        action: "delete",
        description: "Remove teacher records",
        category: "Staff Management",
      },
    ],
  },
  {
    name: "Classes",
    icon: Calendar,
    permissions: [
      {
        id: "13",
        resource: "classes",
        action: "create",
        description: "Create new classes",
        category: "Academic Management",
      },
      {
        id: "14",
        resource: "classes",
        action: "read",
        description: "View class information",
        category: "Academic Management",
      },
      {
        id: "15",
        resource: "classes",
        action: "update",
        description: "Edit class details",
        category: "Academic Management",
      },
      {
        id: "16",
        resource: "classes",
        action: "delete",
        description: "Remove classes",
        category: "Academic Management",
      },
      {
        id: "17",
        resource: "classes",
        action: "manage",
        description: "Full class management",
        category: "Academic Management",
      },
    ],
  },
  {
    name: "Grades",
    icon: BarChart3,
    permissions: [
      {
        id: "18",
        resource: "grades",
        action: "create",
        description: "Add new grades",
        category: "Academic Management",
      },
      {
        id: "19",
        resource: "grades",
        action: "read",
        description: "View grades",
        category: "Academic Management",
      },
      {
        id: "20",
        resource: "grades",
        action: "update",
        description: "Edit grades",
        category: "Academic Management",
      },
      {
        id: "21",
        resource: "grades",
        action: "delete",
        description: "Remove grades",
        category: "Academic Management",
      },
      {
        id: "22",
        resource: "grades",
        action: "manage",
        description: "Full grade management",
        category: "Academic Management",
      },
    ],
  },
  {
    name: "Assignments",
    icon: FileText,
    permissions: [
      {
        id: "23",
        resource: "assignments",
        action: "create",
        description: "Create assignments",
        category: "Academic Management",
      },
      {
        id: "24",
        resource: "assignments",
        action: "read",
        description: "View assignments",
        category: "Academic Management",
      },
      {
        id: "25",
        resource: "assignments",
        action: "update",
        description: "Edit assignments",
        category: "Academic Management",
      },
      {
        id: "26",
        resource: "assignments",
        action: "delete",
        description: "Remove assignments",
        category: "Academic Management",
      },
      {
        id: "27",
        resource: "assignments",
        action: "manage",
        description: "Full assignment management",
        category: "Academic Management",
      },
    ],
  },
  {
    name: "System",
    icon: Settings,
    permissions: [
      {
        id: "28",
        resource: "system",
        action: "configure",
        description: "System configuration",
        category: "System Administration",
      },
      {
        id: "29",
        resource: "roles",
        action: "manage",
        description: "Manage roles and permissions",
        category: "System Administration",
      },
      {
        id: "30",
        resource: "reports",
        action: "view",
        description: "View system reports",
        category: "System Administration",
      },
    ],
  },
];

const allPermissions = resources.flatMap((resource) => resource.permissions);

export function PermissionManagement() {
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [rolePermissions, setRolePermissions] = useState<string[]>([]);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const roles = [
    { id: "1", name: "Super Administrator" },
    { id: "2", name: "Principal" },
    { id: "3", name: "Teacher" },
    { id: "4", name: "Student" },
    { id: "5", name: "Parent" },
  ];

  const categories = [
    "all",
    ...Array.from(new Set(allPermissions.map((p) => p.category))),
  ];

  const filteredPermissions = allPermissions.filter((permission) => {
    const matchesSearch =
      permission.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || permission.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePermissionToggle = (permissionId: string) => {
    setRolePermissions((prev) =>
      prev.includes(permissionId)
        ? prev.filter((id) => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  const handleSelectAll = () => {
    setRolePermissions(filteredPermissions.map((p) => p.id));
  };

  const handleDeselectAll = () => {
    setRolePermissions([]);
  };

  const getActionColor = (action: string) => {
    const colors = {
      create:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      read: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      update:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      delete: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      manage:
        "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      configure:
        "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
      view: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
    };
    return colors[action as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground">
            Permission Management
          </h2>
          <p className="text-muted-foreground">
            Manage permissions using resource-action methodology
          </p>
        </div>
        <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Assign Permissions
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
            <DialogHeader>
              <DialogTitle>Assign Permissions to Role</DialogTitle>
              <DialogDescription>
                Select a role and assign specific permissions using the
                resource-action model.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 flex-1 overflow-hidden">
              <div>
                <Label htmlFor="role-select">Select Role</Label>
                <Select value={selectedRole} onValueChange={setSelectedRole}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedRole && (
                <div className="space-y-4 flex-1 overflow-hidden flex flex-col">
                  <div className="flex gap-4 items-center">
                    <div className="flex-1 relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search permissions..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Select
                      value={selectedCategory}
                      onValueChange={setSelectedCategory}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category === "all" ? "All Categories" : category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleSelectAll}
                      >
                        Select All
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleDeselectAll}
                      >
                        Deselect All
                      </Button>
                    </div>
                  </div>

                  <div className="flex-1 overflow-auto border rounded-lg">
                    <Table>
                      <TableHeader className="sticky top-0 bg-background">
                        <TableRow>
                          <TableHead className="w-12">
                            <Checkbox
                              checked={
                                filteredPermissions.length > 0 &&
                                filteredPermissions.every((p) =>
                                  rolePermissions.includes(p.id)
                                )
                              }
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  handleSelectAll();
                                } else {
                                  handleDeselectAll();
                                }
                              }}
                            />
                          </TableHead>
                          <TableHead>Resource</TableHead>
                          <TableHead>Action</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Category</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredPermissions.map((permission) => (
                          <TableRow key={permission.id}>
                            <TableCell>
                              <Checkbox
                                checked={rolePermissions.includes(
                                  permission.id
                                )}
                                onCheckedChange={() =>
                                  handlePermissionToggle(permission.id)
                                }
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              {permission.resource}
                            </TableCell>
                            <TableCell>
                              <Badge
                                className={getActionColor(permission.action)}
                              >
                                {permission.action}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-sm">
                              {permission.description}
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {permission.category}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    {rolePermissions.length} of {filteredPermissions.length}{" "}
                    permissions selected
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsAssignDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={() => setIsAssignDialogOpen(false)}>
                Save Permissions
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Permission Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Permission Overview
            </CardTitle>
            <CardDescription>
              All available permissions organized by resource
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {resources.map((resource) => (
                <div key={resource.name} className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <resource.icon className="h-5 w-5 text-primary" />
                    <h4 className="font-semibold">{resource.name}</h4>
                    <Badge variant="secondary">
                      {resource.permissions.length} permissions
                    </Badge>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {resource.permissions.map((permission) => (
                      <Badge
                        key={permission.id}
                        className={getActionColor(permission.action)}
                      >
                        {permission.action}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Permission Matrix */}
        <Card>
          <CardHeader>
            <CardTitle>Permission Matrix</CardTitle>
            <CardDescription>
              Quick view of all permissions and their actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Resource</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Category</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {allPermissions.slice(0, 10).map((permission) => (
                  <TableRow key={permission.id}>
                    <TableCell className="font-medium">
                      {permission.resource}
                    </TableCell>
                    <TableCell>
                      <Badge className={getActionColor(permission.action)}>
                        {permission.action}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {permission.category}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <div className="mt-4 text-center">
              <Button variant="outline" size="sm">
                View All {allPermissions.length} Permissions
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

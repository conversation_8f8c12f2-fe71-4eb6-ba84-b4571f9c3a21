"use client";

import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Settings, Shield, Search, RefreshCcw, Loader2 } from "lucide-react";
import { Permission } from "@/lib/types";
import CustomDialog from "@/components/shared/CustomDialog";
import {
  assignRolePermissions,
  getRolePermissions,
} from "@/lib/server/roles/roles.api";
import { showErrorToast, showSuccessToast } from "@/lib/toast-utils";

export function PermissionManagement({
  permissions,
  roleOptions,
}: {
  permissions: Permission[];
  roleOptions: { label: string; value: string }[];
}) {
  const [showAll, setShowAll] = useState(false);

  function groupPermissionsByResource(permissions: Permission[]) {
    const grouped: Record<string, Permission[]> = {};

    for (const perm of permissions) {
      if (!grouped[perm.resource]) {
        grouped[perm.resource] = [];
      }
      grouped[perm.resource].push(perm);
    }

    return Object.entries(grouped).map(([resource, perms]) => ({
      name: resource.charAt(0).toUpperCase() + resource.slice(1),
      permissions: perms,
    }));
  }

  const resources = groupPermissionsByResource(permissions);

  const getActionColor = (action: string) => {
    const colors = {
      create:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      read: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      update:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      delete: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      manage:
        "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      configure:
        "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
      view: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
    };
    return colors[action as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground">
            Permission Management
          </h2>
          <p className="text-muted-foreground">
            Manage permissions using resource-action methodology
          </p>
        </div>
        <CustomDialog
          title="Assign Permissions to Role"
          description="Select a role and assign specific permissions using the
                resource-action model."
          trigger={
            <Button>
              <Shield className="h-4 w-4" />
              Assign Permissions
            </Button>
          }
        >
          <AssignRolePermission
            roleOptions={roleOptions}
            permissions={permissions}
            getActionColor={getActionColor}
            resourceNames={resources.map((resource) => resource.name)}
          />
        </CustomDialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Permission Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Permission Overview
            </CardTitle>
            <CardDescription>
              All available permissions organized by resource
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {resources.map((resource) => (
                <div key={resource.name} className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    {/* <resource.icon className="h-5 w-5 text-primary" /> */}
                    <h4 className="font-semibold">{resource.name}</h4>
                    <Badge variant="secondary">
                      {resource.permissions.length} permissions
                    </Badge>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {resource.permissions.map((permission) => (
                      <Badge
                        key={permission.id}
                        className={getActionColor(permission.action)}
                      >
                        {permission.action}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Permission Matrix */}
        <Card>
          <CardHeader>
            <CardTitle>Permission Matrix</CardTitle>
            <CardDescription>
              Quick view of all permissions and their actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Resource</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Name</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(showAll ? permissions : permissions.slice(0, 10)).map(
                  (permission) => (
                    <TableRow key={permission.id}>
                      <TableCell className="font-medium">
                        {permission.resource}
                      </TableCell>
                      <TableCell>
                        <Badge className={getActionColor(permission.action)}>
                          {permission.action}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {permission.name}
                      </TableCell>
                    </TableRow>
                  )
                )}
              </TableBody>
            </Table>
            <div className="mt-4 text-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAll((prev) => !prev)}
              >
                {showAll
                  ? "Show Less"
                  : `View All ${permissions.length} Permissions`}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function AssignRolePermission({
  roleOptions,
  permissions,
  getActionColor,
  resourceNames,
}: {
  roleOptions: { label: string; value: string }[];
  permissions: Permission[];
  getActionColor: (action: string) => string;
  resourceNames: string[];
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [rolePermissions, setRolePermissions] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedRole, setSelectedRole] = useState<string>("");

  useEffect(() => {
    async function fetchRolePermissions() {
      if (selectedRole) {
        const rolePermissions = await getRolePermissions(selectedRole);
        setRolePermissions(rolePermissions.map((p) => p.id));
        setIsLoading(false);
      }
    }

    fetchRolePermissions();
  }, [selectedRole]);

  const categories = ["all", ...resourceNames];

  const filteredPermissions = permissions.filter((permission) => {
    const matchesSearch =
      permission.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" ||
      permission.resource === selectedCategory.toLocaleLowerCase();
    return matchesSearch && matchesCategory;
  });

  const handleRoleChange = (role: string) => {
    setIsLoading(true);
    setSelectedRole(role);
  };

  const handlePermissionToggle = (permissionId: string) => {
    setRolePermissions((prev) =>
      prev.includes(permissionId)
        ? prev.filter((id) => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  const handleSelectAll = () => {
    setRolePermissions(filteredPermissions.map((p) => p.id));
  };

  const handleDeselectAll = () => {
    setRolePermissions([]);
  };

  const handleSaveChanges = async () => {
    try {
      setIsSaving(true);
      const response = await assignRolePermissions(
        selectedRole,
        rolePermissions
      );
      if (response.error) {
        console.error(response.error);
        showErrorToast("Failed to save changes", response.error);
        return;
      }
      showSuccessToast("Changes saved");
    } catch (error) {
      console.error("Error saving changes:", error);
      showErrorToast("Failed to save changes");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-4 flex-1 overflow-hidden">
      <div className="flex gap-4 justify-between items-center">
        <div className="space-y-2">
          <Label htmlFor="role-select">Select Role</Label>
          <Select value={selectedRole} onValueChange={handleRoleChange}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a role" />
            </SelectTrigger>
            <SelectContent>
              {roleOptions.map((role) => (
                <SelectItem key={role.value} value={role.value}>
                  {role.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button onClick={handleSaveChanges} size="sm" disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" /> Saving...
            </>
          ) : (
            "Save Changes"
          )}
        </Button>
      </div>
      {selectedRole && (
        <div className="space-y-4 flex-1 overflow-hidden flex flex-col">
          <div className="flex gap-4 items-center">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search permissions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category === "all" ? "All Categories" : category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleSelectAll}>
                Select All
              </Button>
              <Button variant="outline" size="sm" onClick={handleDeselectAll}>
                Deselect All
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="flex-1 flex justify-center items-center p-12">
              <div className="flex items-center justify-center">
                <RefreshCcw className="w-6 h-6 text-gray-400 animate-spin mr-2" />
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-auto border rounded-lg">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={
                          filteredPermissions.length > 0 &&
                          filteredPermissions.every((p) =>
                            rolePermissions.includes(p.id)
                          )
                        }
                        onCheckedChange={(checked) => {
                          if (checked) {
                            handleSelectAll();
                          } else {
                            handleDeselectAll();
                          }
                        }}
                      />
                    </TableHead>
                    <TableHead>Resource</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Name</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPermissions.map((permission) => (
                    <TableRow key={permission.id}>
                      <TableCell>
                        <Checkbox
                          checked={rolePermissions.includes(permission.id)}
                          onCheckedChange={() =>
                            handlePermissionToggle(permission.id)
                          }
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        {permission.resource}
                      </TableCell>
                      <TableCell>
                        <Badge className={getActionColor(permission.action)}>
                          {permission.action}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm">
                        {permission.description}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {permission.name}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            {rolePermissions.length} of {filteredPermissions.length} permissions
            selected
          </div>
        </div>
      )}
    </div>
  );
}

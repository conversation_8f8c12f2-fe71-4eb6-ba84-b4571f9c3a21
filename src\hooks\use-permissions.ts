"use client";

import { checkPermission, Actions, Resources } from "@/lib/permissions";
import { Context } from "@/lib/types";
import { Session } from "next-auth";
import { useSession } from "next-auth/react";

export function usePermissions(sessionUser?: Session["user"]) {
  if (sessionUser) {
    return {
      hasPermission: (
        resource: string,
        action: string = Actions.READ,
        context?: Context,
      ) => {
        return checkPermission(sessionUser, resource, action, context);
      },
      user: sessionUser,
      isLoading: false,
    };
  }
  const { data: session, status } = useSession();
  const user = session?.user;
  const isLoading = status === "loading";

  const hasPermission = (
    resource: string,
    action: string = Actions.READ,
    context?: Context,
  ): boolean => {
    if (!user) return false;
    return checkPermission(user, resource, action, context);
  };

  const canCreate = (resource: string, context?: any) =>
    hasPermission(resource, Actions.CREATE, context);

  const canRead = (resource: string, context?: any) =>
    hasPermission(resource, Actions.READ, context);

  const canUpdate = (resource: string, context?: any) =>
    hasPermission(resource, Actions.UPDATE, context);

  const canDelete = (resource: string, context?: any) =>
    hasPermission(resource, Actions.DELETE, context);

  const canManage = (resource: string, context?: any) =>
    hasPermission(resource, Actions.MANAGE, context);

  return {
    hasPermission,
    canCreate,
    canRead,
    canUpdate,
    canDelete,
    canManage,
    user: session?.user,
    isLoading,
  };
}

"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { Actions, Resources } from "@/lib/permissions";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { getLocalDateString } from "@/lib/date-converter";
import CustomDialog from "@/components/shared/CustomDialog";
import { EmploymentInfoForm } from "../../_components/forms/employment.form";
import type { StaffEmployment } from "../../_types/type";

export const columns: ColumnDef<StaffEmployment>[] = [
  {
    accessorKey: "startDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Period" />
    ),
    cell: ({ row }) => {
      const startDate = row.original.startDate as Date;
      const endDate = row.original.endDate as Date | null;
      return (
        <div className="flex flex-wrap gap-2">
          <p>{getLocalDateString(startDate)}</p>
          <p className="text-sm text-muted-foreground">-</p>
          <p>{endDate ? getLocalDateString(endDate) : "Present"}</p>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "department",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Department" />
    ),
    cell: ({ row }) => {
      const department = row.getValue("department") as {
        id: string;
        name: string;
      };
      return <span>{department.name}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "designation",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Designation" />
    ),
    cell: ({ row }) => {
      const designation = row.getValue("designation") as {
        id: string;
        name: string;
      };
      return <span>{designation.name}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "employmentStatus",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Employment Status" />
    ),
    enableSorting: false,
  },
  {
    id: "actions",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => {
      const department = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="data-[state=open]:bg-muted size-8"
            >
              <MoreHorizontal />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            <DropdownMenuItem>View</DropdownMenuItem>
            <ClientRoleGuard
              resource={Resources.STAFFS}
              action={Actions.MANAGE}
            >
              <DropdownMenuItem asChild>
                <CustomDialog
                  title="Edit Department"
                  description="Edit the department"
                  asChild={false}
                  trigger={<span>Edit</span>}
                >
                  <EmploymentInfoForm
                    initialData={{
                      id: department.id,
                      startDate: department.startDate,
                      department: department.department.id,
                      designation: department.designation.id,
                      employmentStatus: department.employmentStatus,
                    }}
                  />
                </CustomDialog>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <CustomAlertDialog
                  title="Delete Department"
                  description="Are you sure you want to delete this department? This action cannot be undone."
                  onConfirm={() => console.log("Delete clicked")}
                  className="w-full"
                  asChild={false}
                  trigger={<span>Delete</span>}
                />
                {/* <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut> */}
              </DropdownMenuItem>
            </ClientRoleGuard>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { Actions, Resources } from "@/lib/permissions";
import PageWrapper from "../_components/layouts/PageWrapper";
import StaffTable from "./table";
import { withResourceAccess } from "@/components/shared/page-gurad";
import Link from "next/link";

const breadcrumbItems = [{ label: "Home", href: "/" }, { label: "Staff" }];

function StaffsPage() {
  const school = "kingdom";
  const renderButton = () => {
    return (
      <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
        <Button asChild>
          <Link href={`/${school}/staffs/new`}>
            <Plus /> Add Staff
          </Link>
        </Button>
      </ClientRoleGuard>
    );
  };

  return (
    <PageWrapper
      pgTitle="Manage Staff"
      pgDescription="Manage staff records and information"
      breadcrumbItems={breadcrumbItems}
      headerButton={renderButton()}
      school={school}
    >
      <StaffTable />
    </PageWrapper>
  );
}

export default withResourceAccess(StaffsPage, {
  resource: Resources.STAFFS,
});

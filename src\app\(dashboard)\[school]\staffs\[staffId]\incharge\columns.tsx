"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/shared/data-table/data-table-column-header";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import { Actions, Resources } from "@/lib/permissions";
import { CustomSheet } from "@/components/shared/CustomSheet";
import CustomAlertDialog from "@/components/shared/CustomAlertDialog";
import { getLocalDateString } from "@/lib/date-converter";

type Props = {
  id: string;
  startDate: Date;
  endDate: Date | null;
  details: string;
};

export const columns: ColumnDef<Props>[] = [
  {
    accessorKey: "startDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Period" />
    ),
    cell: ({ row }) => {
      const startDate = row.original.startDate as Date;
      const endDate = row.original.endDate as Date | null;
      return (
        <div className="flex flex-wrap gap-2">
          <p>{getLocalDateString(startDate)}</p>
          <p className="text-sm text-muted-foreground">-</p>
          <p>{endDate ? getLocalDateString(endDate) : "Present"}</p>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "details",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Details" />
    ),
    enableSorting: false,
  },
  {
    id: "actions",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => {
      const department = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="data-[state=open]:bg-muted size-8"
            >
              <MoreHorizontal />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            <DropdownMenuItem>View</DropdownMenuItem>
            <ClientRoleGuard
              resource={Resources.STAFFS}
              action={Actions.MANAGE}
            >
              <DropdownMenuItem asChild>
                <CustomSheet
                  title="Edit Department"
                  className="w-full"
                  asChild={false}
                  trigger={<span>Edit</span>}
                >
                  <div>Edit Department Form</div>
                </CustomSheet>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <CustomAlertDialog
                  title="Delete Department"
                  description="Are you sure you want to delete this department? This action cannot be undone."
                  onConfirm={() => console.log("Delete clicked")}
                  className="w-full"
                  asChild={false}
                  trigger={<span>Delete</span>}
                />
                {/* <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut> */}
              </DropdownMenuItem>
            </ClientRoleGuard>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  FormInput,
  FormSelect,
  FormRadioGroup,
  FormCombobox,
  FormDatePicker,
} from "@/components/forms";
import z from "zod";
import { getNationality } from "@/data/getNationality";
import {
  enrollReason,
  genderOptions,
  gradeOptions,
  religiousAffiliation,
  studentType,
} from "@/data/options";
import EnrolmentFormSubitButton from "./submit-button";

interface PersonalInfoFormProps {
  initialData?: Partial<PersonalInfoFormData>;
}

const personalInfoSchema = z.object({
  grade: z.string().min(1, "Please select a grade"),
  jhsGrade: z.string().optional(),
  jhsGraduationDate: z.date().optional(),
  strand: z.string().optional(),
  specialization: z.string().optional(),
  id: z.string().optional(),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  middleName: z.string().min(2, "Middle name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  otherName: z.string().min(2, "Other name must be at least 2 characters"),
  religiousAffiliation: z
    .string()
    .min(1, "Please select a religious affiliation"),
  cra: z.string().min(2, "City must be at least 2 characters"),
  barangay: z.string().min(2, "Barangay must be at least 2 characters"),
  city: z.string().min(2, "City must be at least 2 characters"),
  pobProvince: z.string().min(2, "Privince must be at least 2 characters"),
  pobCity: z.string().min(2, "City must be at least 2 characters"),
  nationality: z.string().min(2, "Nationality must be at least 2 characters"),
  passportNumber: z
    .string()
    .min(2, "Passport number must be at least 2 characters"),
  visaStatus: z.string().min(2, "Visa status must be at least 2 characters"),
  arcNumber: z.string().min(2, "ARC number must be at least 2 characters"),
  authorizedStay: z
    .string()
    .min(2, "Autorized stay must be at least 2 characters"),
  email: z.email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  otherPhone: z.string().min(10, "Phone number must be at least 10 digits"),
  dateOfBirth: z.date({
    error: "Date of birth is required",
  }),
  gender: z.enum(["male", "female", "other"], {
    error: "Please select a gender",
  }),
  lrn: z.string().min(2, "LRN must be at least 2 characters"),
  esc: z.string().min(2, "ESC must be at least 2 characters"),
  qvr: z.string().min(2, "QVR must be at least 2 characters"),
  profilePicture: z.string().optional(),
});

type PersonalInfoFormData = z.infer<typeof personalInfoSchema>;

export function PersonalInfoForm({ initialData }: PersonalInfoFormProps) {
  const [nationalityData, setNationalityData] = useState<
    { label: string; value: string }[]
  >([]);

  const form = useForm<PersonalInfoFormData>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      grade: undefined,
      jhsGrade: "",
      jhsGraduationDate: new Date(),
      strand: "",
      specialization: "",
      firstName: "",
      middleName: "",
      lastName: "",
      otherName: "",
      cra: "",
      barangay: "",
      city: "",
      pobProvince: "",
      pobCity: "",
      nationality: "PH",
      passportNumber: "",
      visaStatus: "",
      arcNumber: "",
      authorizedStay: "",
      religiousAffiliation: undefined,
      email: "",
      phone: "",
      otherPhone: "",
      dateOfBirth: new Date(),
      gender: undefined,
      lrn: "",
      esc: "",
      qvr: "",
      ...initialData,
    },
  });

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }

    const fetchNationality = async () => {
      try {
        const data = await getNationality();
        setNationalityData(data);
      } catch (error) {
        console.error("Failed to fetch nationality data:", error);
      }
    };
    fetchNationality();
  }, [initialData, form]);

  const handleSubmit = async (data: PersonalInfoFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  const selectedGradeLevel = Number(form.watch("grade"));

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Academic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Academic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormCombobox
                  form={form}
                  name="grade"
                  label="Incoming Grade Level"
                  placeholder="Select grade"
                  options={gradeOptions}
                  searchPlaceholder="Search grades..."
                />
                <FormCombobox
                  form={form}
                  name="studentType"
                  label="Student Type"
                  placeholder="Select student type"
                  options={studentType}
                  searchPlaceholder="Search types..."
                />
                {selectedGradeLevel >= 11 && selectedGradeLevel > 0 && (
                  <>
                    <FormInput
                      form={form}
                      name="jhsGrade"
                      label="Junior High School Grade"
                      placeholder="Enter grade"
                    />
                    <FormDatePicker
                      form={form}
                      name="jhsGraduationDate"
                      label="Junior High School Graduation Date"
                      placeholder="Select graduation date"
                    />
                    <FormInput
                      form={form}
                      name="strand"
                      label="Strand"
                      placeholder="Enter strand"
                    />
                    <FormInput
                      form={form}
                      name="specialization"
                      label="Specialization"
                      placeholder="Enter specialization"
                    />
                  </>
                )}
              </div>
              {form.watch("grade") !== undefined && (
                <FormCombobox
                  form={form}
                  name="enrollReason"
                  label="Reaseon for Re-enrolling or Transfering (optional)"
                  placeholder="Select reason"
                  options={enrollReason}
                  searchPlaceholder="Search reason..."
                />
              )}
            </div>

            <Separator />

            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Personal Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="firstName"
                  label="First Name"
                  placeholder="Enter first name"
                />
                <FormInput
                  form={form}
                  name="middleName"
                  label="Middle Name"
                  placeholder="Enter middle name"
                />
                <FormInput
                  form={form}
                  name="lastName"
                  label="Last Name"
                  placeholder="Enter last name"
                />
                <FormInput
                  form={form}
                  name="otherName"
                  label="Other Name"
                  placeholder="Enter other name"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormDatePicker
                  form={form}
                  name="dateOfBirth"
                  label="Date of Birth"
                  placeholder="Select date of birth"
                />
                <FormRadioGroup
                  form={form}
                  name="gender"
                  label="Gender"
                  options={genderOptions}
                  orientation="horizontal"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="cra"
                  label="Current Residential Address"
                  placeholder="Enter your address"
                />
                <FormInput
                  form={form}
                  name="barangay"
                  label="Barangay"
                  placeholder="Enter your barangay"
                />
                <FormInput
                  form={form}
                  name="city"
                  label="City"
                  placeholder="Enter your city"
                />
                <FormInput
                  form={form}
                  name="pobProvince"
                  label="Place of Birth - Province"
                  placeholder="Enter a province"
                />
                <FormInput
                  form={form}
                  name="pobCity"
                  label="Place of Birth - city/Municipality"
                  placeholder="Enter a city"
                />
                <FormSelect
                  form={form}
                  name="nationality"
                  label="Nationality"
                  placeholder="Select nationality"
                  options={nationalityData}
                />
                {form.watch("nationality") !== "PH" &&
                  form.watch("nationality") !== undefined && (
                    <>
                      <FormInput
                        form={form}
                        name="passportNumber"
                        label="Passport Number"
                        placeholder="Enter your passport number"
                      />
                      <FormInput
                        form={form}
                        name="visaStatus"
                        label="Visa Status"
                        placeholder="Enter your visa status"
                      />
                      <FormInput
                        form={form}
                        name="arcNumber"
                        label="Arc Number"
                        placeholder="Enter your arc number"
                      />
                      <FormInput
                        form={form}
                        name="authorizedStay"
                        label="Authorized Stay"
                        placeholder="Enter your authorized stay"
                      />
                    </>
                  )}
                <FormSelect
                  form={form}
                  name="religiousAffiliation"
                  label="Religious Affiliation"
                  placeholder="Select religious affiliation"
                  options={religiousAffiliation}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="email"
                  label="Email Address"
                  type="email"
                  placeholder="<EMAIL>"
                />
                <FormInput
                  form={form}
                  name="phone"
                  label="Phone Number"
                  type="tel"
                  placeholder="(*************"
                />
                <FormInput
                  form={form}
                  name="otherPhone"
                  label="Other Phone Number"
                  type="tel"
                  placeholder="(*************"
                />
              </div>
            </div>

            <Separator />

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">
                Government-issued ID/Voucher no.
              </h3>
              <FormInput
                form={form}
                name="lrn"
                label="Learner Reference Number - LRN"
                placeholder="Enter your lrn"
              />
              <FormInput
                form={form}
                name="esc"
                label="Education Service Contracting ID - ESC"
                placeholder="Enter your ESC"
              />
              <FormInput
                form={form}
                name="qvr"
                label="Qaulified Voucher Recipient - QVR"
                placeholder="Enter your QVR"
              />
            </div>

            {/* Form Actions */}
            <EnrolmentFormSubitButton
              isSumitting={form.formState.isSubmitting}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

import {
  BarChart2,
  BookOpen,
  Building,
  CalendarRange,
  ChartAreaIcon,
  ChartColumn,
  DollarSign,
  Dot,
  File,
  FileCode2,
  FileMinusIcon,
  GraduationCap,
  Heart,
  HelpCircle,
  LayoutDashboard,
  MessageCircle,
  NotebookPen,
  Printer,
  Settings,
  User2,
  Users2,
} from "lucide-react";
import { Resources } from "../lib/permissions";

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ElementType<any, keyof React.JSX.IntrinsicElements>;
  href: string;
  resource: string;
  children?: NavigationItem[];
  badge?: string;
  isExact?: boolean;
}

export const NAVIGATION_CONFIG: NavigationItem[] = [
  {
    id: "dashboard",
    label: "Dashboard",
    href: "/dashboard",
    resource: Resources.DASHBOARD,
    icon: LayoutDashboard,
  },
  {
    id: "academic",
    label: "Academic",
    icon: File,
    href: "/academic",
    resource: Resources.ACADEMICS,
    children: [
      {
        id: "department",
        label: "Department",
        icon: Dot,
        href: "/academics/departments",
        resource: Resources.ACADEMIC_DEPARTMENTS,
        isExact: true,
      },
      {
        id: "session",
        label: "Session",
        icon: Dot,
        href: "/academics/sessions",
        resource: Resources.SESSIONS,
      },
      {
        id: "subject",
        label: "Subject",
        icon: Dot,
        href: "/academics/subjects",
        resource: Resources.SUBJECTS,
      },
      {
        id: "time-table",
        label: "Time Table",
        icon: Dot,
        href: "/academics/time-tables",
        resource: Resources.TIMETABLES,
      },
      {
        id: "certificate",
        label: "Certificate",
        icon: Dot,
        href: "/academics/certificates",
        resource: Resources.CERTIFICATES,
      },
      {
        id: "id-card",
        label: "ID Card",
        icon: Dot,
        href: "/academics/id-cards",
        resource: Resources.ID_CARDS,
      },
    ],
  },
  {
    id: "staff",
    label: "Staff",
    icon: Users2,
    href: "/staff",
    resource: Resources.STAFFS,
    children: [
      {
        id: "staff",
        label: "Staffs",
        icon: Dot,
        href: "/staffs",
        resource: Resources.STAFFS,
        isExact: true,
      },
      {
        id: "department",
        label: "Department",
        icon: Dot,
        href: "/staffs/departments",
        resource: Resources.STAFF_DEPARTMENTS,
      },
      {
        id: "designation",
        label: "Designation",
        icon: Dot,
        href: "/staffs/designations",
        resource: Resources.DESIGNATIONS,
      },
      {
        id: "attendance",
        label: "Attendance",
        icon: Dot,
        href: "/staffs/attendances",
        resource: Resources.ATTENDANCE,
      },
      {
        id: "leave",
        label: "Leave",
        icon: Dot,
        href: "/staffs/leaves",
        resource: Resources.LEAVE,
      },
      {
        id: "payroll",
        label: "Payroll",
        icon: Dot,
        href: "/staffs/payrolls",
        resource: Resources.PAYROLLS,
      },
    ],
  },
  {
    id: "student",
    label: "Student",
    icon: Users2,
    href: "/students",
    resource: Resources.STUDENTS,
    children: [
      {
        id: "student",
        label: "Students",
        icon: Dot,
        href: "/students",
        resource: Resources.STUDENTS,
        isExact: true,
      },
      {
        id: "registration",
        label: "Registration",
        icon: Dot,
        href: "/students/registrations",
        resource: Resources.REGISTRATIONS,
        isExact: true,
      },
      {
        id: "health-record",
        label: "Health Record",
        icon: Dot,
        href: "/students/health-records",
        resource: Resources.HEALTH_RECORDS,
      },
      {
        id: "attendance",
        label: "Attendance",
        icon: Dot,
        href: "/students/attendances",
        resource: Resources.ATTENDANCE,
      },
      {
        id: "promotion",
        label: "Promotion",
        icon: Dot,
        href: "/students/promotions",
        resource: Resources.PROMOTIONS,
      },
      {
        id: "fee-allocation",
        label: "Fee Allocation",
        icon: Dot,
        href: "/students/fee-allocations",
        resource: Resources.FEE_ALLOCATIONS,
      },
      {
        id: "leave-request",
        label: "Leave Request",
        icon: Dot,
        href: "/students/leave-requests",
        resource: Resources.LEAVE_REQUESTS,
      },
      {
        id: "report",
        label: "Report",
        icon: Dot,
        href: "/students/reports",
        resource: Resources.REPORTS,
      },
    ],
  },
  {
    id: "roles-permissions",
    label: "Roles & Permissions",
    icon: User2,
    href: "/roles-permissions",
    resource: Resources.ROLES,
  },
  {
    id: "classes",
    label: "Classes",
    icon: Building,
    href: "/classes",
    resource: Resources.CLASSES,
    children: [
      {
        id: "classes_list",
        label: "All Classes",
        icon: Dot,
        href: "/classes",
        resource: Resources.CLASSES,
        isExact: true,
      },
      {
        id: "classes_add",
        label: "Create Class",
        icon: Dot,
        href: "/classes/add",
        resource: Resources.CLASSES,
      },
    ],
  },
  {
    id: "grades",
    label: "Grades",
    icon: GraduationCap,
    href: "/grades",
    resource: Resources.GRADES,
  },
  {
    id: "user",
    label: "Users",
    icon: User2,
    href: "/users",
    resource: Resources.USERS,
  },
  {
    id: "attendance",
    label: "Attendance",
    icon: CalendarRange,
    href: "/attendance",
    resource: Resources.ATTENDANCE,
  },
  {
    id: "assignments",
    label: "Assignments",
    icon: NotebookPen,
    href: "/assignments",
    resource: Resources.SUBJECTS, // Using SUBJECTS as closest match for assignments
  },
  {
    id: "communications",
    label: "Messages",
    icon: MessageCircle,
    href: "/communications",
    resource: Resources.CONTACTS,
  },
  {
    id: "fees",
    label: "Fees",
    icon: ChartColumn,
    href: "/fees",
    resource: Resources.FEES,
  },
  {
    id: "reports",
    label: "Reports",
    icon: ChartAreaIcon,
    href: "/reports",
    resource: Resources.REPORTS,
  },
  {
    id: "settings",
    label: "Settings",
    icon: Settings,
    href: "/settings",
    resource: Resources.SETTINGS,
    children: [
      {
        id: "settings_school",
        label: "School Settings",
        icon: Dot,
        href: "/settings/school",
        resource: Resources.SETTINGS,
      },
      {
        id: "settings_roles",
        label: "Manage Roles",
        icon: Dot,
        href: "/settings/roles",
        resource: Resources.ROLES,
      },
      {
        id: "settings_users",
        label: "Manage Users",
        icon: Dot,
        href: "/settings/users",
        resource: Resources.USERS,
      },
    ],
  },

  // enrollment
  {
    id: "form",
    label: "Admission Form",
    icon: FileCode2,
    href: "/enrollment/form",
    resource: Resources.ENROLLMENT_FORM,
    children: [
      {
        id: "personal",
        label: "Personal Information",
        icon: User2,
        href: "/enrollment/form/personal",
        resource: Resources.ENROLLMENT_FORM,
      },
      {
        id: "education",
        label: "Educational Background",
        icon: BookOpen,
        href: "/enrollment/form/education",
        resource: Resources.ENROLLMENT_FORM,
      },
      {
        id: "family",
        label: "Family Background",
        icon: Users2,
        href: "/enrollment/form/family",
        resource: Resources.ENROLLMENT_FORM,
      },
      {
        id: "health",
        label: "Health Survey",
        icon: Heart,
        href: "/enrollment/form/health",
        resource: Resources.ENROLLMENT_FORM,
      },
      {
        id: "document",
        label: "Documentation",
        icon: FileMinusIcon,
        href: "/enrollment/form/document",
        resource: Resources.ENROLLMENT_FORM,
      },
      {
        id: "other",
        label: "Other Information",
        icon: BarChart2,
        href: "/enrollment/form/other",
        resource: Resources.ENROLLMENT_FORM,
      },
    ],
  },
  {
    id: "printout",
    label: "Printout",
    icon: Printer,
    href: "/printout",
    resource: Resources.PRINTOUT,
    children: [
      {
        id: "exam",
        label: "Exam Information",
        icon: User2,
        href: "/enrollment/printout/exam-info",
        resource: Resources.PRINTOUT,
      },
      {
        id: "student",
        label: "Student Information",
        icon: BookOpen,
        href: "/enrollment/printout/student-info",
        resource: Resources.PRINTOUT,
      },
    ],
  },
  {
    id: "payment",
    label: "Payment",
    icon: DollarSign,
    href: "/enrollment/payment",
    resource: Resources.PAYMENT,
  },
  {
    id: "help",
    label: "Help and Support",
    icon: HelpCircle,
    href: "/enrollment/help",
    resource: Resources.HELP,
  },

  {
    id: "profile",
    label: "Profile",
    icon: User2,
    href: "/profile",
    resource: Resources.DASHBOARD, // Everyone can access their profile, using dashboard as default
  },
];

// export function getFilteredNavigation(userPermissions: string[]): NavigationItem[] {
//   return NAVIGATION_CONFIG.filter(item => {
//     // Dashboard is accessible to everyone
//     if (item.resource === Resources.DASHBOARD) return true;

//     // Check if user has permission for this resource
//     return userPermissions.some(permission => {
//       const [resource] = permission.split('.');
//       return resource === item.resource;
//     });
//   }).map(item => ({
//     ...item,
//     children: item.children?.filter(child => {
//       // Dashboard is accessible to everyone
//       if (child.resource === Resources.DASHBOARD) return true;

//       // Check if user has permission for this resource
//       return userPermissions.some(permission => {
//         const [resource] = permission.split('.');
//         return resource === child.resource;
//       });
//     }),
//   }));
// }

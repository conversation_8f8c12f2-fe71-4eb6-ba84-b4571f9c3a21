import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";

const QualificationInfo = [
  {
    degree: "B.Tech",
    specialization: "Computer Science",
    institution: "XYZ University",
    yearOfPassing: "2022",
    percentage: "85%",
  },
  {
    degree: "M.Tech",
    specialization: "Artificial Intelligence",
    institution: "ABC University",
    yearOfPassing: "2024",
    percentage: "90%",
  },
];

function QualificationPage() {
  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Qualification</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2 bg-transparent"
          >
            Add Qualification
          </Button>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {QualificationInfo.length !== 0 ? (
        <NoDataPlaceholder
          title="Manage all Employee Qualification Records"
          description="Keep the documents related to your staff's qualification."
        >
          <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
            <CustomDialog
              title="Add Qualification"
              description="Add a new qualification for the staff"
              trigger={<Button>Add Qualification</Button>}
            >
              <div>Add Qualification Form</div>
            </CustomDialog>
          </ClientRoleGuard>
        </NoDataPlaceholder>
      ) : (
        <div>
          {QualificationInfo.map((qualification) => (
            <div key={qualification.degree}>
              <h2 className="text-lg font-semibold">{qualification.degree}</h2>
              <p className="text-sm text-muted-foreground">
                {qualification.specialization}
              </p>
              <p className="text-sm text-muted-foreground">
                {qualification.institution}
              </p>
              <p className="text-sm text-muted-foreground">
                Year of Passing: {qualification.yearOfPassing}
              </p>
              <p className="text-sm text-muted-foreground">
                Percentage: {qualification.percentage}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default withResourceAccess(QualificationPage, {
  resource: Resources.STAFFS,
});

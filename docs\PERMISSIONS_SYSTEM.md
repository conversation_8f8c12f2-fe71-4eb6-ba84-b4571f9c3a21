# Permissions System Documentation

## Overview

The Kingdom SIS Web application implements a comprehensive Role-Based Access Control (RBAC) system with the following components:

- **Resources**: Different areas/modules of the application (e.g., dashboard, students, teachers)
- **Actions**: Operations that can be performed on resources (create, read, update, delete, manage)
- **Permissions**: Combinations of resources and actions (e.g., `students:read`, `grades:manage`)
- **Roles**: Collections of permissions assigned to users
- **Role-Permission Assignments**: Links between roles and their permissions
- **User-Role Assignments**: Links between users and their roles

## Database Schema

### Core Tables

1. **permissions** - Stores all available permissions
2. **roles** - Stores system and custom roles
3. **role_permissions_assignment** - Links roles to permissions
4. **user_roles_assignment** - Links users to roles

## System Roles

### 1. Super Admin (`super_admin`)
- **Description**: Super Administrator with all permissions
- **Permissions**: Full access to everything (`*:manage`)
- **Use Case**: System administrators, developers

### 2. School Admin (`admin`)
- **Description**: School Administrator with limited permissions
- **Permissions**: 
  - Dashboard access
  - Manage students, teachers, classes
  - Read grades, attendance, reports
  - Manage settings, users, roles
- **Use Case**: School administrators, principals

### 3. Principal (`principal`)
- **Description**: Principal with management permissions
- **Permissions**: Similar to admin but focused on academic oversight
- **Use Case**: School principals, academic directors

### 4. Teacher (`teacher`)
- **Description**: Teacher with classroom management permissions
- **Permissions**:
  - Dashboard access
  - Read students
  - Manage grades and attendance (own classes only)
  - Read reports (own classes only)
- **Use Case**: Teaching staff

### 5. Student (`student`)
- **Description**: Student with limited view permissions
- **Permissions**:
  - Dashboard access
  - Read own grades and attendance
  - Read enrolled classes
- **Use Case**: Students

### 6. Parent (`parent`)
- **Description**: Parent with view permissions for their children
- **Permissions**:
  - Dashboard access
  - Read children's information
  - Read children's grades and attendance
- **Use Case**: Parents/guardians

### 7. Applicant (`applicant`)
- **Description**: Applicant with limited view permissions
- **Permissions**:
  - Dashboard access
  - Manage enrollment forms (own records)
  - Read payment information (own records)
  - Access help resources
- **Use Case**: Prospective students

## Resources and Actions

### Resources
- `dashboard` - Main dashboard
- `academics` - Academic management
- `academic_departments` - Academic departments
- `classes` - Class management
- `users` - User management
- `sessions` - Session management
- `subjects` - Subject management
- `timetables` - Timetable management
- `certificates` - Certificate management
- `grades` - Grade management
- `id_cards` - ID card management
- `staffs` - Staff management
- `staff_departments` - Staff departments
- `contacts` - Contact management
- `teachers` - Teacher management
- `designations` - Designation management
- `payrolls` - Payroll management
- `leave` - Leave management
- `students` - Student management
- `registrations` - Registration management
- `health_records` - Health records
- `promotions` - Promotion management
- `leave_requests` - Leave requests
- `attendance` - Attendance management
- `reports` - Report generation
- `settings` - System settings
- `roles` - Role management
- `fees` - Fee management
- `fee_allocations` - Fee allocations
- `payment` - Payment management
- `enrollment_form` - Enrollment forms
- `printout` - Printout management
- `help` - Help resources

### Actions
- `create` - Create new records
- `read` - View/read records
- `update` - Modify existing records
- `delete` - Remove records
- `manage` - Full access (create, read, update, delete)
- `take` - Special action for taking attendance

## Seeding the Database

### Full Database Seed
Run the main seed file to create all data including roles and permissions:

```bash
npx prisma db seed
```

### Roles and Permissions Only
Run the dedicated roles and permissions seed file:

```bash
npx tsx prisma/seed-roles-permissions.ts
```

### What Gets Created

1. **222 Permissions** (37 resources × 6 actions each)
2. **7 System Roles** with appropriate permissions
3. **Role-Permission Assignments** linking roles to their permissions
4. **Sample Users** with role assignments:
   - 1 Super Admin
   - 3 Teachers with teacher role
   - 250 Students with student role

## Usage Examples

### Checking Permissions in Code

```typescript
import { checkPermission } from '@/lib/permissions';
import { Resources, Actions } from '@/lib/permissions';

// Check if user can read students
const canReadStudents = checkPermission(user, Resources.STUDENTS, Actions.READ);

// Check if user can manage grades with context
const canManageGrades = checkPermission(
  user, 
  Resources.GRADES, 
  Actions.MANAGE,
  { teacherIds: [user.id] } // Context for own classes only
);
```

### Using Permission Checker Class

```typescript
import { PermissionChecker } from '@/lib/permissions';

const checker = new PermissionChecker(user);

if (checker.canAccess(Resources.STUDENTS, Actions.READ)) {
  // User can read students
}
```

## Conditional Permissions

Some permissions include conditions that limit access:

- **ownRecords**: User can only access their own records
- **ownChildren**: Parent can only access their children's records  
- **ownClasses**: Teacher can only access classes they teach
- **enrolledClasses**: Student can only access classes they're enrolled in

## API Endpoints

- `GET /api/roles` - Get all roles with permissions
- `GET /api/roles/permissions` - Get all permissions
- `POST /api/roles/assign` - Assign permission to role

## Extending the System

### Adding New Resources
1. Add to `Resources` enum in `src/lib/permissions.ts`
2. Re-run the permissions seed to create new permissions
3. Assign to appropriate roles

### Adding New Actions
1. Add to `Actions` enum in `src/lib/permissions.ts`
2. Re-run the permissions seed to create new permissions
3. Update role definitions as needed

### Creating Custom Roles
1. Use the admin interface or API to create new roles
2. Assign appropriate permissions
3. Assign roles to users

## Security Considerations

- Super admin role should be limited to trusted system administrators
- Regular audits of role assignments should be performed
- Permissions should follow the principle of least privilege
- Context-based permissions should be properly validated
- All permission checks should be performed server-side

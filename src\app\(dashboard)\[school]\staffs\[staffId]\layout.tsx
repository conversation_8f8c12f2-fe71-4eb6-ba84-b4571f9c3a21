import SideNav from "../_components/navigation/side-nav";

export default async function StaffLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<Record<string, string>>;
}) {
  const { school, staffId } = await params;

  return (
    <div className="flex gap-5 w-full">
      <SideNav school={school} staffId={staffId} />
      <div className="flex-1">{children}</div>
    </div>
  );
}

#!/usr/bin/env node

/**
 * Script to seed roles and permissions data
 * Usage: node scripts/seed-permissions.js
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting permissions seeding process...\n');

try {
  // Change to project root directory
  const projectRoot = path.resolve(__dirname, '..');
  process.chdir(projectRoot);

  console.log('📍 Working directory:', process.cwd());
  console.log('🔧 Running permissions seed script...\n');

  // Run the permissions seed script
  execSync('npx tsx prisma/seed-roles-permissions.ts', {
    stdio: 'inherit',
    cwd: projectRoot
  });

  console.log('\n🎉 Permissions seeding completed successfully!');
  console.log('\n📚 Next steps:');
  console.log('   1. Check the database to verify roles and permissions were created');
  console.log('   2. Assign roles to users through the admin interface');
  console.log('   3. Test permissions in the application');
  console.log('\n📖 For more information, see docs/PERMISSIONS_SYSTEM.md');

} catch (error) {
  console.error('\n❌ Error during permissions seeding:');
  console.error(error.message);
  process.exit(1);
}

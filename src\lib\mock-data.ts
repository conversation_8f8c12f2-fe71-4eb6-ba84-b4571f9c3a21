import type {
  User,
  Admin,
  Teacher,
  Student,
  Parent,
  Class,
  Subject,
  Assignment,
  Grade,
  Attendance,
  SuperAdmin,
  Academic_Department,
  Staff_Department,
  Designation,
  Staff,
} from "./types";
import { SYSTEM_ROLES } from "./permissions";
import { staffStatus, staffType } from "@/data/options";
import { faker } from "@faker-js/faker";
import { ColumnFiltersState, PaginationState, SortingState } from "@tanstack/react-table";

// Mock Users
export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "<PERSON>",
    permissions: SYSTEM_ROLES.SUPER_ADMIN.permissions,
    avatar: "/professional-woman-admin.png",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "<PERSON>",
    permissions: SYSTEM_ROLES.ADMIN.permissions,
    avatar: "/professional-woman-admin.png",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  } as Admin,
  {
    id: "3",
    email: "<EMAIL>",
    name: "<PERSON>",
    permissions: SYSTEM_ROLES.TEACHER.permissions,
    avatar: "/male-teacher.png",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "4",
    email: "<EMAIL>",
    name: "Emily Davis",
    permissions: SYSTEM_ROLES.STUDENT.permissions,
    avatar: "/diverse-female-student.png",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "5",
    email: "<EMAIL>",
    name: "Robert Davis",
    permissions: SYSTEM_ROLES.PARENT.permissions,
    avatar: "/parent-father.png",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "6",
    email: "<EMAIL>",
    name: "Alice Doe",
    permissions: SYSTEM_ROLES.APPLICANT.permissions,
    avatar: "/images/placeholder.svg",
    school: "kingdom",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

// Mock detailed user data
export const mockSuperAdmins: SuperAdmin[] = [
  {
    ...mockUsers[0],
    role: ["super_admin"],
  } as SuperAdmin,
];

export const mockAdmins: Admin[] = [
  {
    ...mockUsers[1],
    role: ["admin"],
  } as Admin,
];

export const mockTeachers: Teacher[] = [
  {
    ...mockUsers[2],
    role: ["teacher"],
    employeeId: "T001",
    subjects: ["mathematics", "physics", "english"],
    classes: ["10A", "11B"],
    department: "Science",
  } as Teacher,
];

export const mockStudents: Student[] = [
  {
    ...mockUsers[3],
    role: ["student"],
    studentId: "S001",
    grade: "10",
    class: "10A",
    parentId: "4",
    dateOfBirth: new Date("2008-05-15"),
  } as Student,
];

export const mockParents: Parent[] = [
  {
    ...mockUsers[4],
    role: ["parent"],
    children: ["3"],
    phone: "******-0123",
  } as Parent,
];

// Mock Classes
export const mockClasses: Class[] = [
  {
    id: "1",
    name: "10A",
    grade: "10",
    teacherId: "3",
    students: ["4"],
    subjects: ["mathematics", "physics", "english"],
  },
];

// Mock Subjects
export const mockSubjects: Subject[] = [
  {
    id: "1",
    name: "Mathematics",
    code: "MATH10",
    teacherId: "3",
    classes: ["10A"],
  },
  {
    id: "2",
    name: "Physics",
    code: "PHYS10",
    teacherId: "3",
    classes: ["10A"],
  },
];

// Mock Assignments
export const mockAssignments: Assignment[] = [
  {
    id: "1",
    title: "Algebra Quiz",
    description: "Basic algebra problems covering linear equations",
    subjectId: "1",
    classId: "1",
    teacherId: "2",
    dueDate: new Date("2024-02-15"),
    createdAt: new Date("2024-02-01"),
  },
];

// Mock Grades
export const mockGrades: Grade[] = [
  {
    id: "1",
    studentId: "3",
    assignmentId: "1",
    score: 85,
    maxScore: 100,
    feedback: "Good work! Focus on quadratic equations.",
    gradedAt: new Date("2024-02-16"),
  },
];

// Mock Attendance
export const mockAttendance: Attendance[] = [
  {
    id: "1",
    studentId: "3",
    classId: "1",
    date: new Date("2024-02-01"),
    status: "present",
  },
];

// Mock Academic Departments
export const mockDepartments: Academic_Department[] = [
  {
    id: "1",
    name: "Computer Science",
    code: "CS",
    alias: "CompSci",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    name: "Mathematics",
    code: "MATH",
    alias: "Math",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "3",
    name: "Physics",
    code: "PHYS",
    alias: "Physics",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "4",
    name: "Chemistry",
    code: "CHEM",
    alias: "Chem",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "5",
    name: "Biology",
    code: "BIO",
    alias: "Bio",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "6",
    name: "Engineering",
    code: "ENG",
    alias: "Engineering",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "7",
    name: "Business Administration",
    code: "BUS",
    alias: "Business",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "8",
    name: "Fine Arts",
    code: "ARTS",
    alias: "Arts",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "9",
    name: "English Literature",
    code: "ENG-LIT",
    alias: "English",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "10",
    name: "History",
    code: "HIST",
    alias: "History",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "11",
    name: "Psychology",
    code: "PSYC",
    alias: "Psychology",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "12",
    name: "Physical Education",
    code: "PE",
    alias: "PhysEd",
    description: "Department of Computer Science and Information Technology",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export const mockStaffDepartments: Staff_Department[] = [
  {
    id: "1",
    name: "Administrative",
    alias: "Admin",
    description: "Administrative Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    name: "Registrar",
    alias: "Reg",
    description: "Registrar Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "3",
    name: "Teaching",
    alias: "Teach",
    description: "Teaching Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "4",
    name: "Human Resource",
    alias: "HR",
    description: "Human Resource Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "5",
    name: "Finance",
    alias: "Fin",
    description: "Finance Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "6",
    name: "IT",
    alias: "IT",
    description: "IT Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "7",
    name: "Marketing",
    alias: "Mark",
    description: "Marketing Department",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
];

export const mockDesignations: Designation[] = [
  {
    id: "1",
    name: "Administrator",
    alias: "Admin",
    parent: null,
    description: "Admin Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    name: "Director",
    alias: "Dir",
    parent: {
      id: "1",
      name: "Administrator",
      alias: "Admin",
    },
    description: "Director Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "3",
    name: "Manager",
    alias: "Mgr",
    parent: {
      id: "2",
      name: "Director",
      alias: "Dir",
    },
    description: "Manager Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "4",
    name: "Principal",
    alias: "Principal",
    parent: {
      id: "3",
      name: "Manager",
      alias: "Mgr",
    },
    description: "Principal Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "5",
    name: "Teacher",
    alias: "Teacher",
    parent: {
      id: "4",
      name: "Principal",
      alias: "Principal",
    },
    description: "Teacher Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "6",
    name: "Helper",
    alias: "Helper",
    parent: {
      id: "2",
      name: "Director",
      alias: "Director",
    },
    description: "Helper Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "7",
    name: "Driver",
    alias: "Driver",
    parent: {
      id: "2",
      name: "Director",
      alias: "Director",
    },
    description: "Driver Designation",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  }
];

// Generate mock staff data using Faker.js
const generateMockStaffs = (count: number): Staff[] => {
  const staffs: Staff[] = [];

  for (let i = 1; i <= count; i++) {
    const gender = faker.helpers.arrayElement(['male', 'female']);
    const firstName = faker.person.firstName(gender as 'male' | 'female');
    const lastName = faker.person.lastName();
    const middleName = faker.person.middleName();
    const name = `${firstName} ${middleName} ${lastName}`;

    // Generate realistic birth dates (ages 25-65)
    const dateOfBirth = faker.date.birthdate({ min: 25, max: 65, mode: 'age' });

    // Generate join dates (between 1-20 years ago, with weighted distribution)
    const joinDate = faker.date.between({
      from: new Date(new Date().getFullYear() - 20, 0, 1),
      to: new Date()
    });

    // Generate phone number (10 digits)
    const phone = faker.string.numeric(10);

    // Generate email based on name
    const email = faker.internet.email({
      firstName: firstName.toLowerCase(),
      lastName: lastName.toLowerCase(),
      provider: 'school.edu'
    });

    // Generate staff code with prefix and number
    const code = `S${String(i).padStart(3, '0')}`;

    // Weighted distribution for staff types (more teaching staff)
    const type = faker.helpers.weightedArrayElement([
      { weight: 60, value: staffType[1].value }, // teaching
      { weight: 25, value: staffType[0].value }, // administrative
      { weight: 15, value: staffType[2].value }, // support
    ]);

    // Weighted distribution for staff status (more permanent staff)
    const status = faker.helpers.weightedArrayElement([
      { weight: 70, value: staffStatus[2].value }, // permanent
      { weight: 20, value: staffStatus[1].value }, // contract
      { weight: 10, value: staffStatus[0].value }, // probation
    ]);

    // Select department and designation based on staff type
    let department: string;
    let designation: string;

    if (type === 'teaching') {
      department = mockStaffDepartments[2].id; // Teaching department
      designation = faker.helpers.arrayElement([
        mockDesignations[3].id, // Manager
        mockDesignations[4].id, // Supervisor
        mockDesignations[5].id, // Teacher
      ]);
    } else if (type === 'administrative') {
      department = faker.helpers.arrayElement([
        mockStaffDepartments[0].id, // Administrative
        mockStaffDepartments[1].id, // Registrar
        mockStaffDepartments[3].id, // Human Resource
      ]);
      designation = faker.helpers.arrayElement([
        mockDesignations[0].id, // Administrator
        mockDesignations[1].id, // Director
        mockDesignations[2].id, // Manager
      ]);
    } else {
      // support staff
      department = faker.helpers.arrayElement(mockStaffDepartments).id;
      designation = faker.helpers.arrayElement([
        mockDesignations[6].id, // Driver
        mockDesignations[4].id, // Supervisor
      ]);
    }

    // Generate timestamps
    const createdAt = faker.date.between({
      from: joinDate,
      to: new Date()
    });
    const updatedAt = faker.date.between({
      from: createdAt,
      to: new Date()
    });

    staffs.push({
      id: String(i),
      firstName,
      name,
      middleName,
      lastName,
      gender,
      dateOfBirth,
      phone,
      email,
      code,
      joinDate,
      type,
      staffStatus: status,
      department,
      designation,
      createdAt,
      updatedAt,
    });
  }

  return staffs;
};

export const mockStaffs: Staff[] = generateMockStaffs(50);

// Function to generate additional staff members if needed
export const generateAdditionalStaffs = (additionalCount: number): Staff[] => {
  const currentMaxId = Math.max(...mockStaffs.map(staff => parseInt(staff.id)));
  return generateMockStaffs(additionalCount).map((staff, index) => ({
    ...staff,
    id: String(currentMaxId + index + 1),
    code: `S${String(currentMaxId + index + 1).padStart(3, '0')}`,
  }));
};

// Helper functions
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find((user) => user.id === id);
};

export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find((user) => user.email === email);
};

export const getClassesByTeacher = (teacherId: string): Class[] => {
  return mockClasses.filter((cls) => cls.teacherId === teacherId);
};

export const getStudentsByClass = (classId: string): Student[] => {
  const classData = mockClasses.find((cls) => cls.id === classId);
  if (!classData) return [];
  return mockStudents.filter((student) =>
    classData.students.includes(student.id),
  );
};

export const getDepartmentById = (id: string): Academic_Department | undefined => {
  return mockDepartments.find((department) => department.id === id);
};

export const getDepartmentByCode = (code: string): Academic_Department | undefined => {
  return mockDepartments.find((department) => department.code === code);
};

// Mock API function for academic departments (similar to real API)
export const getDepartments = async (): Promise<{
  data: Academic_Department[];
  total: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    data: mockDepartments,
    total: mockDepartments.length,
  };
};

// Mock API function for staff departments (similar to real API)
export const getStaffDepartments = async (): Promise<{
  data: Staff_Department[];
  total: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    data: mockStaffDepartments,
    total: mockStaffDepartments.length,
  };
};

export const staffDepartmentOptions = mockStaffDepartments.map((department) => ({
  label: department.name,
  value: department.id,
}));

// Mock API function for designations (similar to real API)
export const getDesignations = async (): Promise<{
  data: Designation[];
  total: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    data: mockDesignations,
    total: mockDesignations.length,
  };
};

export const designationOptions = mockDesignations.map((designation) => ({
  label: designation.name,
  value: designation.id,
}));

export const getStaffs = async (params: {
  pagination: PaginationState;
  sorting: SortingState;
  columnFilters: ColumnFiltersState;
}): Promise<{
  data: Staff[];
  total: number;
  pageCount: number;
  page: number;
  pageSize: number;
}> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  const { pagination, sorting, columnFilters } = params;
  let filteredStaffs = [...mockStaffs];

  // Apply column filters
  if (columnFilters && columnFilters.length > 0) {
    columnFilters.forEach((filter) => {
      const { id, value } = filter;

      if (!value || (Array.isArray(value) && value.length === 0)) return;

      filteredStaffs = filteredStaffs.filter((staff) => {
        switch (id) {
          case 'firstName':
          case 'lastName':
          case 'middleName':
            return staff[id as keyof Staff]
              ?.toString()
              .toLowerCase()
              .includes(String(value).toLowerCase());

          case 'name':
            // Combined name search
            const fullName = `${staff.firstName} ${staff.middleName} ${staff.lastName}`.toLowerCase();
            return fullName.includes(String(value).toLowerCase());

          case 'email':
            return staff.email.toLowerCase().includes(String(value).toLowerCase());

          case 'phone':
            return staff.phone.includes(String(value));

          case 'code':
            return staff.code.toLowerCase().includes(String(value).toLowerCase());

          case 'gender':
            return Array.isArray(value)
              ? value.includes(staff.gender)
              : staff.gender === value;

          case 'type':
            return Array.isArray(value)
              ? value.includes(staff.type)
              : staff.type === value;

          case 'staffStatus':
            return Array.isArray(value)
              ? value.includes(staff.staffStatus)
              : staff.staffStatus === value;

          case 'department':
            return Array.isArray(value)
              ? value.includes(staff.department)
              : staff.department === value;

          case 'designation':
            return Array.isArray(value)
              ? value.includes(staff.designation)
              : staff.designation === value;

          case 'joinDate':
            if (Array.isArray(value) && value.length === 2) {
              const [startDate, endDate] = value;
              const staffJoinDate = new Date(staff.joinDate);
              return staffJoinDate >= new Date(startDate) && staffJoinDate <= new Date(endDate);
            }
            return true;

          case 'dateOfBirth':
            if (Array.isArray(value) && value.length === 2) {
              const [startDate, endDate] = value;
              const staffBirthDate = new Date(staff.dateOfBirth);
              return staffBirthDate >= new Date(startDate) && staffBirthDate <= new Date(endDate);
            }
            return true;

          default:
            return true;
        }
      });
    });
  }

  // Apply sorting
  if (sorting && sorting.length > 0) {
    filteredStaffs.sort((a, b) => {
      for (const sort of sorting) {
        const { id, desc } = sort;
        let aValue: any;
        let bValue: any;

        switch (id) {
          case 'name':
            aValue = `${a.firstName} ${a.lastName}`.toLowerCase();
            bValue = `${b.firstName} ${b.lastName}`.toLowerCase();
            break;
          case 'firstName':
          case 'lastName':
          case 'middleName':
          case 'email':
          case 'phone':
          case 'code':
          case 'gender':
          case 'type':
          case 'staffStatus':
            aValue = a[id as keyof Staff]?.toString().toLowerCase();
            bValue = b[id as keyof Staff]?.toString().toLowerCase();
            break;
          case 'dateOfBirth':
          case 'joinDate':
          case 'createdAt':
          case 'updatedAt':
            aValue = new Date(a[id as keyof Staff] as Date);
            bValue = new Date(b[id as keyof Staff] as Date);
            break;
          case 'department':
            // Sort by department name
            const aDept = mockStaffDepartments.find(d => d.id === a.department);
            const bDept = mockStaffDepartments.find(d => d.id === b.department);
            aValue = aDept?.name.toLowerCase() || '';
            bValue = bDept?.name.toLowerCase() || '';
            break;
          case 'designation':
            // Sort by designation name
            const aDesig = mockDesignations.find(d => d.id === a.designation);
            const bDesig = mockDesignations.find(d => d.id === b.designation);
            aValue = aDesig?.name.toLowerCase() || '';
            bValue = bDesig?.name.toLowerCase() || '';
            break;
          default:
            continue;
        }

        if (aValue < bValue) return desc ? 1 : -1;
        if (aValue > bValue) return desc ? -1 : 1;
      }
      return 0;
    });
  }

  // Get total count after filtering
  const total = filteredStaffs.length;

  // Apply pagination
  const { pageIndex, pageSize } = pagination;
  const startIndex = pageIndex * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedStaffs = filteredStaffs.slice(startIndex, endIndex);

  // Calculate page count
  const pageCount = Math.ceil(total / pageSize);

  return {
    data: paginatedStaffs,
    total,
    pageCount,
    page: pageIndex + 1,
    pageSize,
  };
};

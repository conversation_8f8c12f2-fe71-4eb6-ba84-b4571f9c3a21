export type StaffEmployment = {
  id: string;
  startDate: Date;
  endDate: Date | null;
  department: { id: string; name: string };
  designation: { id: string; name: string };
  employmentStatus: string;
  remarks?: string;
};

export type ContactInfo = {
  firstName: string;
  middleName: string;
  lastName: string;
  dateOfBirth: Date;
  gender: "female" | "male" | "other";
  nationality: string;
  stateOfOrigin: string;
  stateOfResidence: string;
  religiousAffiliation: string;
  bloodGroup: string;
  maritalStatus: string;
  staffType: string;
  id?: string | undefined;
};

export type WorkShift = {
  id: string;
  startDate: Date;
  endDate: Date | null;
  workShift: string;
  remarks?: string;
}
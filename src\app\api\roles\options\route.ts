import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        displayName: true,
      }
    });

    const transformedRoles = roles.map((role) => ({
      label: role.displayName,
      value: role.id,
    })) as { label: string; value: string }[]
    
    return NextResponse.json(transformedRoles);
  } catch (error) {
    console.error("Error fetching role options:", error);
    return NextResponse.json(
      { error: "Failed to fetch role options" },
      { status: 500 },
    );
  }
}
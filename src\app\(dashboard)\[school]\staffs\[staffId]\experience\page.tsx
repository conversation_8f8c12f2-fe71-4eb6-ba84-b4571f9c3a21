import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";

const ExperienceInfo = [
  {
    degree: "B.Tech",
    specialization: "Computer Science",
    institution: "XYZ University",
    yearOfPassing: "2022",
    percentage: "85%",
  },
  {
    degree: "M.Tech",
    specialization: "Artificial Intelligence",
    institution: "ABC University",
    yearOfPassing: "2024",
    percentage: "90%",
  },
];

function ExperiencePage() {
  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Experience</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2 bg-transparent"
          >
            Add Experience
          </Button>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {ExperienceInfo.length !== 0 ? (
        <NoDataPlaceholder
          title="Manage all Employee Experience Records"
          description="Keep the documents related to your staff's experience."
        >
          <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
            <CustomDialog
              title="Add Experience"
              description="Add a new experience for the staff"
              trigger={<Button>Add Experience</Button>}
            >
              <div>Add Experience Form</div>
            </CustomDialog>
          </ClientRoleGuard>
        </NoDataPlaceholder>
      ) : (
        <div>
          {ExperienceInfo.map((experience) => (
            <div key={experience.degree}>
              <h2 className="text-lg font-semibold">{experience.degree}</h2>
              <p className="text-sm text-muted-foreground">
                {experience.specialization}
              </p>
              <p className="text-sm text-muted-foreground">
                {experience.institution}
              </p>
              <p className="text-sm text-muted-foreground">
                Year of Passing: {experience.yearOfPassing}
              </p>
              <p className="text-sm text-muted-foreground">
                Percentage: {experience.percentage}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default withResourceAccess(ExperiencePage, {
  resource: Resources.STAFFS,
});

"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Phone } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const sidebarItems = [
  { id: "basic", label: "Basic", href: "/basic" },
  { id: "contact", label: "Contact", href: "/contact" },
  { id: "employment", label: "Employment", href: "/employment" },
  { id: "goverment-id", label: "Goverment ID", href: "/goverment-id" },
  { id: "incharge", label: "Incharge", href: "/incharge" },
  { id: "work-shift", label: "Work Shift", href: "/work-shift" },
  { id: "user-auth", label: "User Auth", href: "/user-auth" },
  { id: "qualification", label: "Qualification", href: "/qualification" },
  { id: "document", label: "Document", href: "/document" },
  { id: "account", label: "Account", href: "/account" },
];

export default function SideNav({
  school,
  staffId,
}: {
  school: string;
  staffId: string;
}) {
  const pathname = usePathname();

  const activeLink = sidebarItems.find((item) =>
    pathname.includes(item.href)
  )?.id;

  return (
    <aside className="p-4 rounded-lg w-50 border shadow flex flex-col">
      {/* Employee Profile Section */}
      <div className="px-2 text-center">
        <div className="relative mb-4">
          <Avatar className="w-24 h-24 mx-auto">
            <AvatarImage src="/professional-woman-avatar.png" alt="Employee" />
            <AvatarFallback className="bg-red-500 text-xl">BR</AvatarFallback>
          </Avatar>
        </div>
        <h2 className="text-base font-semibold">ESM001</h2>
        <h3 className="text-xl font-bold mb-1">Bagwati Rege</h3>
        <p className="text-xs text-muted-foreground italic mb-1">
          Administration Department
        </p>
        <p className="text-xs text-muted-foreground italic mb-3">
          Administrator
        </p>
        <div className="flex items-center justify-center text-sm">
          <Phone className="w-4 h-4 mr-2" />
          09877322288
        </div>
      </div>

      <Separator className="my-4" />

      {/* Navigation Menu */}
      <nav className="flex-1 space-y-1.5">
        {sidebarItems.map((item) => (
          <Link
            href={`/${school}/staffs/${staffId}${item.href}`}
            key={item.id}
            className={`w-full flex items-center px-2 py-1.5 text-sm text-left hover:text-primary transition-colors ${
              activeLink === item.id
                ? "text-primary border-r-2 border-primary"
                : ""
            }`}
          >
            {item.label}
          </Link>
        ))}
      </nav>
    </aside>
  );
}

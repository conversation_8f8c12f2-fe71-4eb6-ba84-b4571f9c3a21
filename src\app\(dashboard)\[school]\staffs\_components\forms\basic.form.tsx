"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { toast } from "sonner";
import {
  FormInput,
  FormSelect,
  FormRadioGroup,
  FormDatePicker,
} from "@/components/forms";
import z from "zod";
import { getNationality } from "@/data/getNationality";
import {
  bloodGroup,
  genderOptions,
  maritalStatus,
  religiousAffiliation,
  staffType,
  states,
} from "@/data/options";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface BasicInfoFormProps {
  initialData?: Partial<BasicInfoFormData>;
}

const basicInfoSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  middleName: z.string().min(2, "Middle name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  dateOfBirth: z.date({
    error: "Date of birth is required",
  }),
  gender: z.enum(["male", "female", "other"], {
    error: "Please select a gender",
  }),
  nationality: z.string().min(2, "Nationality must be at least 2 characters"),
  stateOfOrigin: z
    .string()
    .min(2, "State of origin must be at least 2 characters"),
  stateOfResidence: z
    .string()
    .min(2, "State of residence must be at least 2 characters"),
  religiousAffiliation: z
    .string()
    .min(1, "Please select a religious affiliation"),
  bloodGroup: z.string().min(2, "Select blood group"),
  maritalStatus: z.string().min(1, "Please select a marital status"),
  staffType: z.string().min(1, "Please select a staff type"),
});

type BasicInfoFormData = z.infer<typeof basicInfoSchema>;

export function BasicInfoForm({ initialData }: BasicInfoFormProps) {
  const [nationalityData, setNationalityData] = useState<
    { label: string; value: string }[]
  >([]);

  const form = useForm<BasicInfoFormData>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      firstName: "",
      middleName: "",
      lastName: "",
      gender: undefined,
      dateOfBirth: new Date(),
      nationality: "PH",
      stateOfOrigin: undefined,
      stateOfResidence: undefined,
      religiousAffiliation: undefined,
      bloodGroup: undefined,
      maritalStatus: undefined,
      staffType: undefined,
      ...initialData,
    },
  });

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }

    const fetchNationality = async () => {
      try {
        const data = await getNationality();
        setNationalityData(data);
      } catch (error) {
        console.error("Failed to fetch nationality data:", error);
      }
    };
    fetchNationality();
  }, [initialData, form]);

  const handleSubmit = async (data: BasicInfoFormData) => {
    try {
      console.log(data);
      // await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Form submission failed", {
        description: "Please check your input and try again.",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  name="firstName"
                  label="First Name"
                  placeholder="Enter first name"
                />
                <FormInput
                  form={form}
                  name="middleName"
                  label="Middle Name"
                  placeholder="Enter middle name"
                />
                <FormInput
                  form={form}
                  name="lastName"
                  label="Last Name"
                  placeholder="Enter last name"
                />
                <FormRadioGroup
                  form={form}
                  name="gender"
                  label="Gender"
                  options={genderOptions}
                  orientation="horizontal"
                />
                <FormDatePicker
                  form={form}
                  name="dateOfBirth"
                  label="Date of Birth"
                  placeholder="Select date of birth"
                />
                <FormSelect
                  form={form}
                  name="nationality"
                  label="Nationality"
                  placeholder="Select nationality"
                  options={nationalityData}
                />
                <FormSelect
                  form={form}
                  name="stateOfOrigin"
                  label="State of Origin"
                  placeholder="Select state of origin"
                  options={states}
                />
                <FormSelect
                  form={form}
                  name="stateOfResidence"
                  label="State of Residence"
                  placeholder="Select state of residence"
                  options={states}
                />
                <FormSelect
                  form={form}
                  name="religiousAffiliation"
                  label="Religious Affiliation"
                  placeholder="Select religious affiliation"
                  options={religiousAffiliation}
                />
                <FormSelect
                  form={form}
                  name="bloodGroup"
                  label="Blood Group"
                  placeholder="Select blood group"
                  options={bloodGroup}
                />
                <FormSelect
                  form={form}
                  name="maritalStatus"
                  label="Marital Status"
                  placeholder="Select marital status"
                  options={maritalStatus}
                />
                <FormSelect
                  form={form}
                  name="staffType"
                  label="Staff Type"
                  placeholder="Select staff type"
                  options={staffType}
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex pt-4">
              <Button
                type="submit"
                className="flex-1 md:flex-none"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>Submit</>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

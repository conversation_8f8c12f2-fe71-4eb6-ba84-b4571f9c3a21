import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { User } from "@/lib/types";

export async function POST(request: NextRequest) {
  const body = await request.json() as { userID: string };

  if (!body.userID) {
    return NextResponse.json(
      { error: "User ID is required" },
      { status: 400 },
    );
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: body.userID },
      include: {
        userRoles: {
          select: {
            id: true,
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
                rolePermissions: {
                  select: {
                    id: true,
                    permission: {
                      select: {
                        id: true,
                        name: true,
                        description: true,
                        resource: true,
                        action: true,
                      },
                    },
                  },
                },
              },
            }
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 },
      );
    }

    const transformedUser: User = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar || "",
      school: user.school,
      isActive: user.isActive,
      roles: user.userRoles.map((role) => ({
        id: role.role.id,
        name: role.role.name,
        displayName: role.role.displayName,
      })),
      permissions: user.userRoles
        .map((role) => role.role.rolePermissions)
        .flat()
        .map((permission) => ({
          id: permission.permission.id,
          name: permission.permission.name,
          description: permission.permission.description,
          resource: permission.permission.resource,
          action: permission.permission.action,
        })),
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 },
    );
  }
}
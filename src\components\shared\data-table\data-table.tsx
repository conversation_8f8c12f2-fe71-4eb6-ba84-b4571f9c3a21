"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
  PaginationState,
  getFilteredRowModel,
  getFacetedUniqueValues,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { DataTablePagination } from "./data-table-pagination";
import { DataTableToolbar } from "./data-table-toolbar";
import { DataTableBulkActions } from "./data-table-bulk-actions";
import { RefreshCcw } from "lucide-react";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  // Server-side props
  loading?: boolean;
  pageCount?: number;
  onPaginationChange?: (pagination: PaginationState) => void;
  onSortingChange?: (sorting: SortingState) => void;
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
  // Manual control flags
  manualPagination?: boolean;
  manualSorting?: boolean;
  manualFiltering?: boolean;
  // Toolbar configuration
  filterConfigs?: Array<{
    columnId: string;
    title: string;
    options: Array<{
      label: string;
      value: string;
      icon?: React.ComponentType<{ className?: string }>;
    }>;
  }>;
  // Legacy support (deprecated - use filterConfigs instead)
  filterOptions?: {
    statuses?: {
      label: string;
      value: string;
      icon?: React.ComponentType<{ className?: string }>;
    }[];
    priorities?: {
      label: string;
      value: string;
      icon?: React.ComponentType<{ className?: string }>;
    }[];
  };
  searchColumn?: string;
  searchPlaceholder?: string;
  // Bulk actions
  bulkActions?: Array<{
    key: string;
    label: string;
    icon?: React.ReactNode;
    variant?:
      | "default"
      | "destructive"
      | "outline"
      | "secondary"
      | "ghost"
      | "link";
    onClick: (selectedRows: any[]) => void;
  }>;
  enableBulkActions?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  loading = false,
  pageCount = -1,
  onPaginationChange,
  onSortingChange,
  onColumnFiltersChange,
  manualPagination = false,
  manualSorting = false,
  manualFiltering = false,
  filterConfigs,
  filterOptions, // Legacy support
  searchColumn = "title",
  searchPlaceholder = "Filter...",
  bulkActions = [],
  enableBulkActions = false,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 25,
  });

  // Handle server-side state changes
  React.useEffect(() => {
    if (manualPagination && onPaginationChange) {
      onPaginationChange(pagination);
    }
  }, [pagination, manualPagination, onPaginationChange]);

  React.useEffect(() => {
    if (manualSorting && onSortingChange) {
      onSortingChange(sorting);
    }
  }, [sorting, manualSorting, onSortingChange]);

  React.useEffect(() => {
    if (manualFiltering && onColumnFiltersChange) {
      onColumnFiltersChange(columnFilters);
    }
  }, [columnFilters, manualFiltering, onColumnFiltersChange]);

  const table = useReactTable({
    data,
    columns,
    pageCount: manualPagination ? pageCount : undefined,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: manualSorting ? undefined : getSortedRowModel(),
    // Only enable client-side filtering models when NOT using manual filtering
    getFilteredRowModel: manualFiltering ? undefined : getFilteredRowModel(),
    getFacetedRowModel: manualFiltering ? undefined : getFilteredRowModel(),
    getFacetedUniqueValues: manualFiltering
      ? undefined
      : getFacetedUniqueValues(),
    manualPagination,
    manualSorting,
    manualFiltering,
  });

  return (
    <div className="flex flex-col gap-4">
      <DataTableToolbar
        table={table}
        filterConfigs={filterConfigs}
        filterOptions={filterOptions}
        searchColumn={searchColumn}
        searchPlaceholder={searchPlaceholder}
      />

      {enableBulkActions && (
        <DataTableBulkActions table={table} bulkActions={bulkActions} />
      )}
      <div className="overflow-hidden rounded-md border">
        <Table>
          <TableHeader className="bg-primary/15">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex items-center justify-center">
                    <RefreshCcw className="w-6 h-6 text-gray-400 animate-spin mr-2" />
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="hover:bg-primary/5"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination
        table={table}
        enableBulkActions={enableBulkActions}
      />
    </div>
  );
}

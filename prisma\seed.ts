import {
  PrismaClient,
  StudentStatus,
  EnrollmentType,
} from "@prisma/client";
import { faker } from "@faker-js/faker";
import { Resources, Actions, SYSTEM_ROLES, ALL_PERMISSIONS } from "../src/lib/permissions";

const prisma = new PrismaClient();

type Department = {
  id: string;
  name: string;
  code: string;
  description: string | null;
};

// Sample data arrays
const departments = [
  {
    name: "Computer Science",
    code: "CS",
    description: "Computer Science and Information Technology",
  },
  {
    name: "Mathematics",
    code: "MATH",
    description: "Pure and Applied Mathematics",
  },
  { name: "Physics", code: "PHYS", description: "Physics and Astronomy" },
  {
    name: "Chemistry",
    code: "CHEM",
    description: "Chemistry and Biochemistry",
  },
  { name: "Biology", code: "BIO", description: "Biology and Life Sciences" },
  {
    name: "Engineering",
    code: "ENG",
    description: "Engineering and Technology",
  },
  {
    name: "Business",
    code: "BUS",
    description: "Business Administration and Management",
  },
  { name: "Arts", code: "ARTS", description: "Fine Arts and Creative Studies" },
];

const grades = ["A", "B", "C", "D", "F"];
const years = [1, 2, 3, 4];
const statuses: StudentStatus[] = [
  "active",
  "inactive",
  "suspended",
  "graduated",
];
const enrollmentTypes: EnrollmentType[] = [
  "fulltime",
  "parttime",
  "online",
  "hybrid",
];

// Helper function to generate student ID
function generateStudentId(year: number, index: number): string {
  return `KIS${year}${String(index).padStart(4, "0")}`;
}

// Helper function to generate realistic GPA
function generateGPA(): number {
  return parseFloat((Math.random() * 2 + 2).toFixed(2)); // GPA between 2.00 and 4.00
}

async function main() {
  console.log("🌱 Starting database seeding...");

  // Clean existing data (in correct order due to foreign key constraints)
  console.log("🧹 Cleaning existing data...");
  await prisma.student.deleteMany();
  await prisma.userRoleAssignment.deleteMany();
  await prisma.rolePermissionAssignment.deleteMany();
  await prisma.role.deleteMany();
  await prisma.permission.deleteMany();
  await prisma.department.deleteMany();
  await prisma.school.deleteMany();
  await prisma.user.deleteMany();

  // Create school
  console.log("🏫 Creating school...");
  const school = await prisma.school.create({
    data: {
      name: "Kingdom International School",
      code: "KIS",
      address: "123 Education Street, Knowledge City, KC 12345",
      phone: "******-0123",
      email: "<EMAIL>",
      website: "https://kingdom.edu",
    },
  });

  // Create departments
  console.log("🏢 Creating departments...");
  const createdDepartments = [];
  for (const dept of departments) {
    const department = await prisma.department.create({
      data: {
        schoolId: school.id,
        name: dept.name,
        code: dept.code,
        description: dept.description,
      },
    });
    createdDepartments.push(department);
  }

  // Create permissions
  console.log("🔐 Creating permissions...");
  const createdPermissions = [];
  for (const permission of ALL_PERMISSIONS) {
    const createdPermission = await prisma.permission.create({
      data: {
        name: permission.name,
        description: permission.description,
        resource: permission.resource,
        action: permission.action,
      },
    });
    createdPermissions.push(createdPermission);
  }

  console.log(`   ✅ Created ${createdPermissions.length} permissions`);

  // Create roles
  console.log("👥 Creating roles...");
  const createdRoles = [];
  for (const [roleKey, roleData] of Object.entries(SYSTEM_ROLES)) {
    const role = await prisma.role.create({
      data: {
        name: roleData.name,
        displayName: roleData.displayName,
        description: roleData.description,
        isSystem: roleData.isSystem,
        isActive: roleData.isActive,
      },
    });
    createdRoles.push({ ...role, permissions: roleData.permissions });
  }

  console.log(`   ✅ Created ${createdRoles.length} roles`);

  // Create role-permission assignments
  console.log("🔗 Creating role-permission assignments...");
  let totalAssignments = 0;

  for (const role of createdRoles) {
    for (const rolePermission of role.permissions) {
      // Handle wildcard permissions (super admin)
      if (rolePermission.resource === "*" && rolePermission.action === "manage") {
        // Assign all permissions to super admin
        for (const permission of createdPermissions) {
          await prisma.rolePermissionAssignment.create({
            data: {
              roleId: role.id,
              permissionId: permission.id,
            },
          });
          totalAssignments++;
        }
      } else {
        // Find matching permission
        const matchingPermission = createdPermissions.find(
          p => p.resource === rolePermission.resource && p.action === rolePermission.action
        );

        if (matchingPermission) {
          await prisma.rolePermissionAssignment.create({
            data: {
              roleId: role.id,
              permissionId: matchingPermission.id,
            },
          });
          totalAssignments++;
        } else {
          console.warn(`   ⚠️  Permission not found: ${rolePermission.resource}:${rolePermission.action}`);
        }
      }
    }
  }

  console.log(`   ✅ Created ${totalAssignments} role-permission assignments`);

  // Create admin user with super_admin role
  console.log("👤 Creating admin user...");
  const adminUser = await prisma.user.create({
    data: {
      email: "<EMAIL>",
      name: "System Administrator",
      school: "kingdom",
      isActive: true,
      emailVerified: true,
    },
  });

  // Assign super_admin role to admin user
  const superAdminRole = createdRoles.find(role => role.name === "super_admin");
  if (superAdminRole) {
    await prisma.userRoleAssignment.create({
      data: {
        userId: adminUser.id,
        roleId: superAdminRole.id,
      },
    });
    console.log("   ✅ Assigned super_admin role to admin user");
  }

  // Create students with users and assign student role
  console.log("👨‍🎓 Creating 200+ students...");
  const studentsToCreate = 250; // Creating 250 students
  const batchSize = 25; // Smaller batches for better performance
  const studentRole = createdRoles.find(role => role.name === "student");

  for (
    let batch = 0;
    batch < Math.ceil(studentsToCreate / batchSize);
    batch++
  ) {
    const batchStart = batch * batchSize;
    const batchEnd = Math.min(batchStart + batchSize, studentsToCreate);

    console.log(`   📚 Creating students ${batchStart + 1} to ${batchEnd}...`);

    // Create users and students in the same transaction for each batch
    const batchData: {
      firstName: string;
      lastName: string;
      email: string;
      year: number;
      department: Department;
      status: StudentStatus;
      grade: string;
      gpa: number;
      enrollmentDate: Date;
      globalIndex: number;
    }[] = [];

    for (let i = batchStart; i < batchEnd; i++) {
      const firstName = faker.person.firstName();
      const lastName = faker.person.lastName();
      const email = faker.internet.email({ firstName, lastName }).toLowerCase();
      const year = faker.helpers.arrayElement(years);
      const department = faker.helpers.arrayElement(createdDepartments);

      // More realistic status distribution
      const status = faker.helpers.weightedArrayElement([
        { weight: 85, value: "active" as StudentStatus },
        { weight: 8, value: "inactive" as StudentStatus },
        { weight: 4, value: "suspended" as StudentStatus },
        { weight: 3, value: "graduated" as StudentStatus },
      ]);

      // Generate more realistic enrollment dates based on year
      const currentYear = new Date().getFullYear();
      const enrollmentYear = currentYear - (4 - year); // 4th year students enrolled 4 years ago
      const enrollmentDate = faker.date.between({
        from: new Date(`${enrollmentYear}-08-01`),
        to: new Date(`${enrollmentYear}-09-30`),
      });

      // Generate GPA based on grade (more realistic correlation)
      let gpaRange = { min: 2.0, max: 4.0 };
      const grade = faker.helpers.arrayElement(grades);
      switch (grade) {
        case "A":
          gpaRange = { min: 3.7, max: 4.0 };
          break;
        case "B":
          gpaRange = { min: 3.0, max: 3.6 };
          break;
        case "C":
          gpaRange = { min: 2.3, max: 2.9 };
          break;
        case "D":
          gpaRange = { min: 2.0, max: 2.2 };
          break;
        case "F":
          gpaRange = { min: 0.0, max: 1.9 };
          break;
      }
      const gpa = parseFloat(
        (Math.random() * (gpaRange.max - gpaRange.min) + gpaRange.min).toFixed(
          2,
        ),
      );

      batchData.push({
        firstName,
        lastName,
        email,
        year,
        department,
        status,
        grade,
        gpa,
        enrollmentDate,
        globalIndex: i,
      });
    }

    // Process batch with transaction
    await prisma.$transaction(async (tx) => {
      for (const data of batchData) {
        // Create user
        const user = await tx.user.create({
          data: {
            email: data.email,
            name: `${data.firstName} ${data.lastName}`,
            isActive: data.status === "active",
            school: "kingdom",
            emailVerified: faker.datatype.boolean({ probability: 0.85 }),
            avatar: faker.image.avatar(),
          },
        });

        // Create student
        await tx.student.create({
          data: {
            userId: user.id,
            schoolId: school.id,
            studentId: generateStudentId(data.year, data.globalIndex + 1),
            status: data.status,
            enrollmentType: faker.helpers.weightedArrayElement([
              { weight: 75, value: "fulltime" as EnrollmentType },
              { weight: 15, value: "parttime" as EnrollmentType },
              { weight: 8, value: "online" as EnrollmentType },
              { weight: 2, value: "hybrid" as EnrollmentType },
            ]),
            grade: data.grade,
            year: data.year,
            departmentId: data.department.id,
            enrollmentDate: data.enrollmentDate,
            graduationDate:
              data.status === "graduated"
                ? faker.date.between({
                    from: new Date("2022-05-01"),
                    to: new Date("2024-06-30"),
                  })
                : null,
            gpa: data.gpa,
          },
        });

        // Assign student role to user
        if (studentRole) {
          await tx.userRoleAssignment.create({
            data: {
              userId: user.id,
              roleId: studentRole.id,
            },
          });
        }
      }
    });
  }

  // Create some sample teachers with teacher role
  console.log("👨‍🏫 Creating sample teachers...");
  const teacherRole = createdRoles.find(role => role.name === "teacher");

  const teacherUsers = await Promise.all([
    prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Dr. John Smith",
        school: "kingdom",
        isActive: true,
        emailVerified: true,
      },
    }),
    prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Prof. Jane Doe",
        school: "kingdom",
        isActive: true,
        emailVerified: true,
      },
    }),
    prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Dr. Mike Wilson",
        school: "kingdom",
        isActive: true,
        emailVerified: true,
      },
    }),
  ]);

  // Assign teacher role to teacher users
  if (teacherRole) {
    for (const teacher of teacherUsers) {
      await prisma.userRoleAssignment.create({
        data: {
          userId: teacher.id,
          roleId: teacherRole.id,
        },
      });
    }
    console.log(`   ✅ Assigned teacher role to ${teacherUsers.length} teachers`);
  }

  // Get final counts
  const userCount = await prisma.user.count();
  const studentCount = await prisma.student.count();
  const departmentCount = await prisma.department.count();
  const schoolCount = await prisma.school.count();
  const roleCount = await prisma.role.count();
  const permissionCount = await prisma.permission.count();
  const rolePermissionCount = await prisma.rolePermissionAssignment.count();
  const userRoleCount = await prisma.userRoleAssignment.count();

  console.log("✅ Seeding completed successfully!");
  console.log(`📊 Created:`);
  console.log(`   🏫 Schools: ${schoolCount}`);
  console.log(`   🏢 Departments: ${departmentCount}`);
  console.log(`   👤 Users: ${userCount}`);
  console.log(`   👨‍🎓 Students: ${studentCount}`);
  console.log(`   👥 Roles: ${roleCount}`);
  console.log(`   🔐 Permissions: ${permissionCount}`);
  console.log(`   🔗 Role-Permission Assignments: ${rolePermissionCount}`);
  console.log(`   👤 User-Role Assignments: ${userRoleCount}`);

  // Show some sample data
  console.log("\n📋 Sample student data:");
  const sampleStudents = await prisma.student.findMany({
    take: 5,
    include: {
      user: true,
      department: true,
      school: true,
    },
  });

  sampleStudents.forEach((student, index) => {
    console.log(
      `   ${index + 1}. ${student.user.name} (${student.studentId}) - ${student.department?.name} - ${student.status}`,
    );
  });
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

import NoDataPlaceholder from "@/components/shared/no-data-placeholder";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { withResourceAccess } from "@/components/shared/page-gurad";
import { Actions, Resources } from "@/lib/permissions";
import { ClientRoleGuard } from "@/components/shared/client-role-guard";
import CustomDialog from "@/components/shared/CustomDialog";

const AccountInfo = [
  {
    degree: "B.Tech",
    specialization: "Computer Science",
    institution: "XYZ University",
    yearOfPassing: "2022",
    percentage: "85%",
  },
  {
    degree: "M.Tech",
    specialization: "Artificial Intelligence",
    institution: "ABC University",
    yearOfPassing: "2024",
    percentage: "90%",
  },
];

function AccountPage() {
  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Account</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2 bg-transparent"
          >
            Add Account
          </Button>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {AccountInfo.length !== 0 ? (
        <NoDataPlaceholder
          title="Manage all Employee Account Records"
          description="Keep the documents related to your staff's account."
        >
          <ClientRoleGuard resource={Resources.STAFFS} action={Actions.CREATE}>
            <CustomDialog
              title="Add Account"
              description="Add a new account for the staff"
              trigger={<Button>Add Account</Button>}
            >
              <div>Add Account Form</div>
            </CustomDialog>
          </ClientRoleGuard>
        </NoDataPlaceholder>
      ) : (
        <div>
          {AccountInfo.map((account) => (
            <div key={account.degree}>
              <h2 className="text-lg font-semibold">{account.degree}</h2>
              <p className="text-sm text-muted-foreground">
                {account.specialization}
              </p>
              <p className="text-sm text-muted-foreground">
                {account.institution}
              </p>
              <p className="text-sm text-muted-foreground">
                Year of Passing: {account.yearOfPassing}
              </p>
              <p className="text-sm text-muted-foreground">
                Percentage: {account.percentage}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default withResourceAccess(AccountPage, {
  resource: Resources.STAFFS,
});

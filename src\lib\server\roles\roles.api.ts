interface CreateRolePayload {
  id?: string;
  name: string;
  displayName: string;
  description: string;
}

export async function getRoles() {
  const response = await fetch(`${process.env.BACKEND_URL}/api/roles`, {
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to fetch roles");
  }

  return response.json();
}

export async function createRole(role: CreateRolePayload) {
  const response = await fetch(`${process.env.BACKEND_URL}/api/roles`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(role),
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to create role");
  }

  return response.json();
}

export async function updateRole(role: Partial<CreateRolePayload>) {
  const response = await fetch(`${process.env.BACKEND_URL}/api/roles`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(role),
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to update role");
  }

  return response.json();
}

export async function deleteRole(roleId: string) {
  const response = await fetch(`${process.env.BACKEND_URL}/api/roles`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ id: roleId }),
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to delete role");
  }

  return response.json();
}

export async function getPermissions() {
  const response = await fetch(
    `${process.env.BACKEND_URL}/api/roles/permissions`,
    {
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch role permissions");
  }

  return response.json();
}

export async function assignRolePermission(roleId: string, permissionId: string) {
  const response = await fetch(
    `${process.env.BACKEND_URL}/api/roles/assign`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ roleId, permissionId }),
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to assign role permission");
  }

  return response.json();
}

'use server';

import { ENV } from "@/config/env";
import { Permission, Role } from "@/lib/types";
import { revalidatePath } from "next/cache";

interface CreateRolePayload {
  id?: string;
  name: string;
  displayName: string;
  description: string;
}

export async function getRoles() {
  const response = await fetch(`${ENV.BACKEND_URL}/api/roles`, {
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to fetch roles");
  }

  return response.json() as Promise<Role[]>;
}

export async function createRole(role: CreateRolePayload) {
  const response = await fetch(`${ENV.BACKEND_URL}/api/roles`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(role),
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to create role");
  }

  revalidatePath("/kingdom/roles-permissions");
  return response.json() as Promise<Role>;
}

export async function updateRole(role: Partial<CreateRolePayload>) {
  const response = await fetch(`${ENV.BACKEND_URL}/api/roles`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(role),
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to update role");
  }

  return response.json();
}

export async function deleteRole(roleId: string) {
  const response = await fetch(`${ENV.BACKEND_URL}/api/roles`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ id: roleId }),
    cache: "no-store",
  });

  if (!response.ok) {
    throw new Error("Failed to delete role");
  }

  return response.json();
}

export async function getPermissions() {
  const response = await fetch(
    `${ENV.BACKEND_URL}/api/roles/permissions`,
    {
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch role permissions");
  }

  return response.json() as Promise<Permission[]>;
}

export async function assignRolePermissions(roleId: string, permissionIds: string[]) {
  const response = await fetch(
    `${ENV.BACKEND_URL}/api/roles/assign`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ roleId, permissionIds }),
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to assign role permission");
  }

  return response.json();
}

export async function assignUserRoles(userID: string, roleIds: string[]) {
  const response = await fetch(
    `${ENV.BACKEND_URL}/api/users/assign-role`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userID, roleIds }),
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to assign user role");
  }

  return response.json();
}

export async function getRoleOptions() {
  const response = await fetch(
    `${ENV.BACKEND_URL}/api/roles/options`,
    {
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch role options");
  }

  return response.json() as Promise<{ label: string; value: string }[]>;
}

export async function getRolePermissions(roleId: string) {
  const response = await fetch(
    `${ENV.BACKEND_URL}/api/roles/rolePermissions?roleId=${roleId}`,
    {
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch role permissions");
  }

  return response.json() as Promise<Permission[]>;
}

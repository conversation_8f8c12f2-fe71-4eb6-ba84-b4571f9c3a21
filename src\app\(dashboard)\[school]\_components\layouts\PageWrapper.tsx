import { BreadcrumbResponsive } from "@/components/shared/breadcrumb";
import { cn } from "@/lib/utils";

type Items = {
  href?: string;
  label: string;
}[];

type Props = {
  pgTitle: string;
  pgDescription?: string;
  pgHeading?: string;
  breadcrumbItems: Items;
  headerButton?: React.ReactNode;
  children: React.ReactNode;
  school: string;
};

const PageWrapper = ({
  pgTitle,
  pgDescription,
  breadcrumbItems,
  headerButton,
  children,
  school,
}: Props) => {
  return (
    <main className="flex flex-col gap-2">
      <BreadcrumbResponsive items={breadcrumbItems} school={school} />
      <div className="mb-4 flex justify-between items-center w-full">
        <div>
          <h1 className="text-2xl font-bold">{pgTitle}</h1>
          {pgDescription ? (
            <p className="text-muted-foreground">{pgDescription}</p>
          ) : null}
        </div>
        <>{headerButton}</>
      </div>

      {children}
    </main>
  );
};

export default PageWrapper;
